<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Player theme with no system UI -->
    <style name="Theme.MvAndroidTV.Player" parent="Theme.MvAndroidTV">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/tv_player_background</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    
    <!-- TV Card Styles -->
    <style name="TVCardStyle">
        <item name="android:layout_width">180dp</item>
        <item name="android:layout_height">240dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:elevation">4dp</item>
    </style>
    
    <style name="TVCardStyle.Large">
        <item name="android:layout_width">240dp</item>
        <item name="android:layout_height">320dp</item>
    </style>
    
    <style name="TVCardStyle.Small">
        <item name="android:layout_width">120dp</item>
        <item name="android:layout_height">160dp</item>
    </style>
    
    <!-- TV Text Styles -->
    <style name="TVTextTitle">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textStyle">bold</item>
        <item name="android:maxLines">2</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_marginTop">8dp</item>
    </style>
    
    <style name="TVTextTitle.Large">
        <item name="android:textSize">20sp</item>
        <item name="android:maxLines">3</item>
    </style>
    
    <style name="TVTextSubtitle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@android:color/darker_gray</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_marginTop">4dp</item>
    </style>
    
    <style name="TVTextDescription">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@android:color/darker_gray</item>
        <item name="android:maxLines">3</item>
        <item name="android:ellipsize">end</item>
        <item name="android:lineSpacingMultiplier">1.2</item>
    </style>
    
    <!-- TV Button Styles -->
    <style name="TVButtonPrimary">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:minWidth">120dp</item>
        <item name="android:background">@android:color/holo_blue_light</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:elevation">2dp</item>
    </style>
    
    <style name="TVButtonSecondary">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:minWidth">120dp</item>
        <item name="android:background">@android:color/darker_gray</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:elevation">2dp</item>
    </style>
    
    <!-- TV Navigation Styles -->
    <style name="TVNavigationTab">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">60dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">14sp</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center</item>
    </style>
    
    <!-- TV Player Control Styles -->
    <style name="TVPlayerControl">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:scaleType">centerInside</item>
    </style>
    
    <style name="TVPlayerControl.Large">
        <item name="android:layout_width">64dp</item>
        <item name="android:layout_height">64dp</item>
    </style>
    
    <!-- TV Progress Bar Styles -->
    <style name="TVProgressBar">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">4dp</item>
        <item name="android:progressDrawable">@android:drawable/progress_horizontal</item>
        <item name="android:indeterminateDrawable">@android:drawable/progress_indeterminate_horizontal</item>
    </style>
    
    <!-- TV Search Styles -->
    <style name="TVSearchBox">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:background">@android:color/darker_gray</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textColorHint">@android:color/darker_gray</item>
        <item name="android:textSize">16sp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
    </style>
    
    <!-- TV List Item Styles -->
    <style name="TVListItem">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">80dp</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:padding">16dp</item>
    </style>
    
    <!-- TV Section Header Styles -->
    <style name="TVSectionHeader">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_marginTop">24dp</item>
        <item name="android:layout_marginBottom">16dp</item>
        <item name="android:layout_marginStart">16dp</item>
        <item name="android:layout_marginEnd">16dp</item>
    </style>
    
    <!-- TV Cloud Provider Styles -->
    <style name="TVCloudProviderItem">
        <item name="android:layout_width">120dp</item>
        <item name="android:layout_height">80dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:background">@android:color/darker_gray</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">12sp</item>
        <item name="android:elevation">2dp</item>
    </style>
    
    <!-- TV Episode Item Styles -->
    <style name="TVEpisodeItem">
        <item name="android:layout_width">100dp</item>
        <item name="android:layout_height">60dp</item>
        <item name="android:layout_margin">4dp</item>
        <item name="android:background">@android:color/darker_gray</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    
    <!-- TV Filter Chip Styles -->
    <style name="TVFilterChip">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">36dp</item>
        <item name="android:layout_margin">4dp</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:background">@android:color/darker_gray</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">12sp</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center</item>
    </style>
    
    <!-- TV Banner Styles -->
    <style name="TVBannerContainer">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">300dp</item>
        <item name="android:background">@android:color/black</item>
    </style>
    
    <style name="TVBannerTitle">
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textStyle">bold</item>
        <item name="android:maxLines">2</item>
        <item name="android:ellipsize">end</item>
        <item name="android:shadowColor">@android:color/black</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">2</item>
    </style>
    
    <style name="TVBannerDescription">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:maxLines">3</item>
        <item name="android:ellipsize">end</item>
        <item name="android:lineSpacingMultiplier">1.2</item>
        <item name="android:shadowColor">@android:color/black</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">2</item>
    </style>
</resources> 