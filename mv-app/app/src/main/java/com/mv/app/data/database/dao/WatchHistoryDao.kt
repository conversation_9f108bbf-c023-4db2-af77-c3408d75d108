package com.mv.app.data.database.dao

import androidx.room.*
import com.mv.app.data.database.entity.WatchHistoryEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface WatchHistoryDao {
    
    @Query("SELECT * FROM watch_history ORDER BY watchedAt DESC")
    fun getAllWatchHistory(): Flow<List<WatchHistoryEntity>>
    
    @Query("SELECT * FROM watch_history WHERE userId = :userId ORDER BY watchedAt DESC")
    fun getWatchHistoryByUser(userId: String): Flow<List<WatchHistoryEntity>>
    
    @Query("SELECT * FROM watch_history WHERE videoId = :videoId AND userId = :userId")
    fun getWatchHistory(videoId: String, userId: String): WatchHistoryEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertWatchHistory(watchHistory: WatchHistoryEntity)
    
    @Update
    fun updateWatchHistory(watchHistory: WatchHistoryEntity)
    
    @Delete
    fun deleteWatchHistory(watchHistory: WatchHistoryEntity)
    
    @Query("DELETE FROM watch_history WHERE userId = :userId")
    fun deleteWatchHistoryByUser(userId: String)
    
    @Query("DELETE FROM watch_history")
    fun deleteAllWatchHistory()
} 