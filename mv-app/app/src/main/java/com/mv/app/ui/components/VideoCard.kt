package com.mv.app.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.mv.app.data.model.Video

@Composable
fun VideoCard(
    video: Video,
    onClick: (Video) -> Unit,
    modifier: Modifier = Modifier,
    isLarge: Boolean = false
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick(video) },
        shape = RoundedCornerShape(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column {
            // 海报图片
            AsyncImage(
                model = getImageUrl(video.pic),
                contentDescription = video.title,
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(if (isLarge) 16f / 9f else 2f / 3f)
                    .clip(RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp)),
                contentScale = ContentScale.Crop
            )
            
            // 视频信息
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                // 标题
                Text(
                    text = video.title ?: "未知标题",
                    style = MaterialTheme.typography.titleMedium,
                    maxLines = if (isLarge) 2 else 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                // 年份和地区
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    video.year?.let { year ->
                        Text(
                            text = year.toString(),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    if (video.year != null && video.area != null) {
                        Text(
                            text = " • ",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    video.area?.let { area ->
                        Text(
                            text = area,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                // 备注信息（状态）
                video.remark?.let { remark ->
                    if (remark.isNotBlank()) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Surface(
                            color = MaterialTheme.colorScheme.primaryContainer,
                            shape = RoundedCornerShape(4.dp)
                        ) {
                            Text(
                                text = remark,
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onPrimaryContainer,
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun VideoCardLarge(
    video: Video,
    onClick: (Video) -> Unit,
    modifier: Modifier = Modifier
) {
    VideoCard(
        video = video,
        onClick = onClick,
        modifier = modifier,
        isLarge = true
    )
}

/**
 * 处理图片URL，确保显示正确的图片
 */
private fun getImageUrl(pic: String?): String {
    return when {
        pic.isNullOrBlank() -> "https://img.jukuku.top/default.jpg"
        pic.startsWith("http://") || pic.startsWith("https://") -> pic
        else -> "https://img.jukuku.top/$pic"
    }
} 