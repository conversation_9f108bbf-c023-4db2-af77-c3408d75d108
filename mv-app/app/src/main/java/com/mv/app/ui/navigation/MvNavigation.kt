package com.mv.app.ui.navigation

import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.mv.app.ui.category.CategoryScreen
import com.mv.app.ui.detail.DetailScreen
import com.mv.app.ui.home.HomeScreen
import com.mv.app.ui.player.PlayerScreen
import com.mv.app.ui.profile.ProfileScreen
import com.mv.app.ui.search.SearchScreen

@Composable
fun MvNavigation(
    navController: NavHostController,
    onNavigateToSidebar: (() -> Unit)? = null,
    onNavigateToDetail: (() -> Unit)? = null,
    onHandleBackKey: (() -> Unit)? = null
) {
    NavHost(
        navController = navController,
        startDestination = MvDestination.Home.route
    ) {
        // 首页
        composable(MvDestination.Home.route) {
            HomeScreen(
                onVideoClick = { video ->
                    navController.navigate(
                        MvDestination.Detail.createRoute(video.id.toString())
                    )
                },
                onBannerClick = { bannerItem ->
                    navController.navigate(
                        MvDestination.Detail.createRoute(bannerItem.id.toString())
                    )
                },
                onNavigateToCategory = { typeId ->
                    val categoryType = when (typeId) {
                        1 -> "movies"
                        2 -> "tv"
                        3 -> "variety"
                        4 -> "anime"
                        5 -> "short"
                        6 -> "documentary"
                        else -> "movies"
                    }
                    navController.navigate(MvDestination.Category.createRoute(categoryType))
                },
                onNavigateToSidebar = onNavigateToSidebar
            )
        }
        
        // 分类页
        composable(
            route = MvDestination.Category.route,
            arguments = MvDestination.Category.arguments
        ) { backStackEntry ->
            val categoryType = backStackEntry.arguments?.getString("categoryType") ?: "movies"
            CategoryScreen(
                navController = navController,
                categoryType = categoryType,
                onNavigateToSidebar = onNavigateToSidebar
            )
        }
        
        // 搜索页
        composable(MvDestination.Search.route) {
            SearchScreen(
                navController = navController,
                onNavigateToSidebar = onNavigateToSidebar
            )
        }
        
        // 我的页面
        composable(MvDestination.Profile.route) {
            ProfileScreen(navController = navController)
        }
        
        // 详情页
        composable(
            route = MvDestination.Detail.route,
            arguments = MvDestination.Detail.arguments
        ) { backStackEntry ->
            val videoId = backStackEntry.arguments?.getString("videoId") ?: ""
            Log.d("MvNavigation", "导航到详情页，videoId: $videoId")

            // 通知MainActivity正在导航到详情页
            LaunchedEffect(Unit) {
                onNavigateToDetail?.invoke()
            }

            DetailScreen(
                navController = navController,
                videoId = videoId,
                onHandleBackKey = onHandleBackKey
            )
        }
        
        // 播放页
        composable(
            route = MvDestination.Player.route,
            arguments = MvDestination.Player.arguments
        ) { backStackEntry ->
            val videoId = backStackEntry.arguments?.getString("videoId") ?: ""
            val episodeId = backStackEntry.arguments?.getString("episodeId") ?: ""
            PlayerScreen(
                navController = navController,
                videoId = videoId,
                episodeId = episodeId
            )
        }
    }
} 