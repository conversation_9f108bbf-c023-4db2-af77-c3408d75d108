package com.mv.app.ui.player.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.mv.app.data.model.PlayListItem

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PlaylistSelector(
    playListByLine: Map<String, List<PlayListItem>>,
    selectedLineId: String?,
    selectedEpisodeId: String?,
    onLineSelected: (String) -> Unit,
    onEpisodeSelected: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            if (playListByLine.isNotEmpty()) {
                // 线路选择
                Text(
                    text = "播放线路",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(playListByLine.keys.toList()) { lineId ->
                        FilterChip(
                            onClick = { onLineSelected(lineId) },
                            label = { 
                                Text(
                                    text = "线路${lineId}",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            },
                            selected = lineId == selectedLineId,
                            colors = FilterChipDefaults.filterChipColors(
                                selectedContainerColor = MaterialTheme.colorScheme.primary,
                                selectedLabelColor = MaterialTheme.colorScheme.onPrimary
                            )
                        )
                    }
                }
                
                // 集数选择
                selectedLineId?.let { lineId ->
                    val episodes = playListByLine[lineId] ?: emptyList()
                    if (episodes.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "选择集数",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        
                        LazyRow(
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            items(episodes) { episode ->
                                FilterChip(
                                    onClick = { onEpisodeSelected(episode.id.toString()) },
                                    label = { 
                                        Text(
                                            text = "第${episode.episode ?: episode.id}集",
                                            style = MaterialTheme.typography.bodyMedium
                                        )
                                    },
                                    selected = episode.id.toString() == selectedEpisodeId,
                                    colors = FilterChipDefaults.filterChipColors(
                                        selectedContainerColor = MaterialTheme.colorScheme.secondary,
                                        selectedLabelColor = MaterialTheme.colorScheme.onSecondary
                                    )
                                )
                            }
                        }
                    }
                }
            } else {
                // 空状态
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "暂无播放列表",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CompactPlaylistSelector(
    availableLines: List<String>,
    availableEpisodes: List<PlayListItem>,
    selectedLineId: String?,
    selectedEpisodeId: String?,
    onLineSelected: (String) -> Unit,
    onEpisodeSelected: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 线路下拉选择
        if (availableLines.isNotEmpty()) {
            var lineExpanded by remember { mutableStateOf(false) }
            
            ExposedDropdownMenuBox(
                expanded = lineExpanded,
                onExpandedChange = { lineExpanded = !lineExpanded },
                modifier = Modifier.weight(1f)
            ) {
                OutlinedTextField(
                    modifier = Modifier.menuAnchor(),
                    readOnly = true,
                    value = selectedLineId?.let { "线路$it" } ?: "选择线路",
                    onValueChange = {},
                    label = { Text("线路") },
                    trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = lineExpanded) },
                    colors = ExposedDropdownMenuDefaults.outlinedTextFieldColors(),
                )
                ExposedDropdownMenu(
                    expanded = lineExpanded,
                    onDismissRequest = { lineExpanded = false },
                ) {
                    availableLines.forEach { lineId ->
                        DropdownMenuItem(
                            text = { Text("线路$lineId") },
                            onClick = {
                                onLineSelected(lineId)
                                lineExpanded = false
                            }
                        )
                    }
                }
            }
        }
        
        // 集数下拉选择
        if (availableEpisodes.isNotEmpty()) {
            var episodeExpanded by remember { mutableStateOf(false) }
            
            ExposedDropdownMenuBox(
                expanded = episodeExpanded,
                onExpandedChange = { episodeExpanded = !episodeExpanded },
                modifier = Modifier.weight(1f)
            ) {
                OutlinedTextField(
                    modifier = Modifier.menuAnchor(),
                    readOnly = true,
                    value = selectedEpisodeId?.let { episodeId ->
                        val episode = availableEpisodes.find { it.id.toString() == episodeId }
                        "第${episode?.episode ?: episode?.id}集"
                    } ?: "选择集数",
                    onValueChange = {},
                    label = { Text("集数") },
                    trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = episodeExpanded) },
                    colors = ExposedDropdownMenuDefaults.outlinedTextFieldColors(),
                )
                ExposedDropdownMenu(
                    expanded = episodeExpanded,
                    onDismissRequest = { episodeExpanded = false },
                ) {
                    availableEpisodes.forEach { episode ->
                        DropdownMenuItem(
                            text = { Text("第${episode.episode ?: episode.id}集") },
                            onClick = {
                                onEpisodeSelected(episode.id.toString())
                                episodeExpanded = false
                            }
                        )
                    }
                }
            }
        }
    }
} 