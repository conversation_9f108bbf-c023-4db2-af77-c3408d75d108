package com.mv.app.ui.components

import android.util.Log
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.*
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.mv.app.data.model.Video

@Composable
fun TVVideoCard(
    video: Video,
    onClick: (Video) -> Unit,
    modifier: Modifier = Modifier,
    isLarge: Boolean = false,
    isFirstInRow: Boolean = false, // 新增：是否是行中的第一个
    onNavigateToSidebar: (() -> Unit)? = null // 新增：导航到侧边栏的回调
) {
    var isFocused by remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    val scale by animateFloatAsState(
        targetValue = if (isFocused) 1.1f else 1f,
        label = "cardScale"
    )
    
    Card(
        onClick = {
            Log.d("TVVideoCard", "卡片被点击，视频ID: ${video.id}, 标题: ${video.title}")
            onClick(video)
        },
        modifier = modifier
            .focusRequester(focusRequester)
            .onFocusChanged { isFocused = it.isFocused }
            .onKeyEvent { keyEvent ->
                when {
                    // 处理左键：只有在第一个卡片时才导航到侧边栏
                    keyEvent.key == Key.DirectionLeft && keyEvent.type == KeyEventType.KeyDown -> {
                        if (isFirstInRow && onNavigateToSidebar != null) {
                            onNavigateToSidebar()
                            true
                        } else {
                            // 让焦点管理器处理正常的左右导航
                            focusManager.moveFocus(androidx.compose.ui.focus.FocusDirection.Left)
                            true
                        }
                    }
                    // 处理右键：正常的焦点移动
                    keyEvent.key == Key.DirectionRight && keyEvent.type == KeyEventType.KeyDown -> {
                        focusManager.moveFocus(androidx.compose.ui.focus.FocusDirection.Right)
                        true
                    }
                    // 处理上下键：正常的焦点移动
                    keyEvent.key == Key.DirectionUp && keyEvent.type == KeyEventType.KeyDown -> {
                        focusManager.moveFocus(androidx.compose.ui.focus.FocusDirection.Up)
                        true
                    }
                    keyEvent.key == Key.DirectionDown && keyEvent.type == KeyEventType.KeyDown -> {
                        focusManager.moveFocus(androidx.compose.ui.focus.FocusDirection.Down)
                        true
                    }
                    // 处理确认键
                    keyEvent.key == Key.Enter && keyEvent.type == KeyEventType.KeyDown -> {
                        Log.d("TVVideoCard", "Enter键被按下，视频ID: ${video.id}")
                        onClick(video)
                        true
                    }
                    keyEvent.key == Key.DirectionCenter && keyEvent.type == KeyEventType.KeyDown -> {
                        Log.d("TVVideoCard", "中心键被按下，视频ID: ${video.id}")
                        onClick(video)
                        true
                    }
                    else -> false
                }
            }
            .scale(scale)
            .border(
                width = if (isFocused) 3.dp else 0.dp,
                color = if (isFocused) MaterialTheme.colorScheme.primary else Color.Transparent,
                shape = RoundedCornerShape(12.dp)
            ),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isFocused) 8.dp else 4.dp
        )
    ) {
        Column {
            // 海报图片
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(if (isLarge) 16f / 9f else 2f / 3f)
            ) {
                AsyncImage(
                    model = getImageUrl(video.pic),
                    contentDescription = video.title,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)),
                    contentScale = ContentScale.Crop
                )
                
                // 状态标签
                video.remark?.let { remark ->
                    if (remark.isNotBlank()) {
                        Surface(
                            modifier = Modifier
                                .align(Alignment.TopEnd)
                                .padding(8.dp),
                            color = MaterialTheme.colorScheme.secondary,
                            shape = RoundedCornerShape(4.dp)
                        ) {
                            Text(
                                text = remark,
                                fontSize = 12.sp,
                                color = Color.White,
                                fontWeight = FontWeight.Medium,
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }
                    }
                }
            }
            
            // 视频信息
            Column(
                modifier = Modifier.padding(12.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                // 标题
                Text(
                    text = video.title ?: "未知标题",
                    fontSize = if (isLarge) 16.sp else 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                    maxLines = if (isLarge) 2 else 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                // 年份和地区
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    video.year?.let { year ->
                        Text(
                            text = year.toString(),
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    if (video.year != null && video.area != null) {
                        Text(
                            text = "•",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    video.area?.let { area ->
                        Text(
                            text = area,
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun TVVideoCardLarge(
    video: Video,
    onClick: (Video) -> Unit,
    modifier: Modifier = Modifier,
    isFirstInRow: Boolean = false,
    onNavigateToSidebar: (() -> Unit)? = null
) {
    TVVideoCard(
        video = video,
        onClick = onClick,
        modifier = modifier,
        isLarge = true,
        isFirstInRow = isFirstInRow,
        onNavigateToSidebar = onNavigateToSidebar
    )
}

private fun getImageUrl(pic: String?): String {
    return when {
        pic.isNullOrBlank() -> "https://img.jukuku.top/default.jpg"
        pic.startsWith("http://") || pic.startsWith("https://") -> pic
        else -> "https://img.jukuku.top/$pic"
    }
} 