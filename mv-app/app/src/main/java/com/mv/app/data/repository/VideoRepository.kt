package com.mv.app.data.repository

import com.mv.app.data.api.MvApiService
import com.mv.app.data.database.dao.VideoDao
import com.mv.app.data.database.entity.VideoEntity
import com.mv.app.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class VideoRepository @Inject constructor(
    private val apiService: MvApiService,
    private val videoDao: VideoDao
) {
    
    /**
     * 获取首页视频数据
     */
    suspend fun getHomeVideos(): Result<HomeData> = try {
        // 获取轮播图数据
        val homeDataResponse = apiService.getHomeData()
        val homeData = if (homeDataResponse.isSuccessful && homeDataResponse.body()?.success == true) {
            homeDataResponse.body()?.data
        } else {
            null
        }
        val bannerItems = homeData?.bannerVideos ?: emptyList()
        val movieVideos = homeData?.movieVideos ?: emptyList()
        val tvVideos = homeData?.tvVideos ?: emptyList()
        val animeVideos = homeData?.animeVideos ?: emptyList()
        val varietyVideos = homeData?.varietyVideos ?: emptyList()
        val documentaryVideos = homeData?.documentaryVideos ?: emptyList()
        val shortVideos = homeData?.shortVideos ?: emptyList()

        Result.success(
            HomeData(
                bannerItems = bannerItems,
                movieVideos = movieVideos,
                tvVideos = tvVideos,
                animeVideos = animeVideos,
                varietyVideos = varietyVideos,
                documentaryVideos = documentaryVideos,
                shortVideos = shortVideos
            )
        )
    } catch (e: Exception) {
        Result.failure(e)
    }

    /**
     * 获取指定分类的视频列表
     */
    suspend fun getCategoryVideos(
        typeId: Int,
        page: Int = 1,
        pageSize: Int = 20,
        area: String? = null,
        year: Int? = null
    ): Result<List<Video>> = try {
        val response = apiService.getVideoList(
            page = page,
            pageSize = pageSize,
            typeId = typeId,
            area = area,
            year = year
        )
        
        if (response.isSuccessful && response.body()?.success == true) {
            val videos = response.body()?.data?.items ?: emptyList()
            Result.success(videos)
        } else {
            Result.failure(Exception(response.body()?.message ?: "获取分类视频失败"))
        }
    } catch (e: Exception) {
        Result.failure(e)
    }

    /**
     * 搜索视频
     */
    suspend fun searchVideos(
        query: String,
        page: Int = 1,
        pageSize: Int = 20
    ): Result<List<Video>> = try {
        val response = apiService.searchVideos(
            query = query,
            page = page,
            pageSize = pageSize
        )
        
        if (response.isSuccessful && response.body()?.success == true) {
            val videos = response.body()?.data?.items ?: emptyList()
            Result.success(videos)
        } else {
            Result.failure(Exception(response.body()?.message ?: "搜索失败"))
        }
    } catch (e: Exception) {
        Result.failure(e)
    }

    /**
     * 获取视频详情
     */
    suspend fun getVideoDetail(id: Int): Result<Video> = try {
        val response = apiService.getVideoDetail(id)
        
        if (response.isSuccessful && response.body()?.success == true) {
            val video = response.body()?.data
            if (video != null) {
                Result.success(video)
            } else {
                Result.failure(Exception("视频不存在"))
            }
        } else {
            Result.failure(Exception(response.body()?.message ?: "获取视频详情失败"))
        }
    } catch (e: Exception) {
        Result.failure(e)
    }

    /**
     * 获取分类列表
     */
    fun getCategories(): List<VideoCategory> {
        return listOf(
            VideoCategory(1, "电影"),
            VideoCategory(2, "电视剧"),
            VideoCategory(3, "动漫"),
            VideoCategory(4, "综艺"),
            VideoCategory(5, "纪录片"),
            VideoCategory(7, "短剧")
        )
    }
    suspend fun getRecommendVideos(): Result<List<Video>> = try {
        val response = apiService.recommend()
        if (response.isSuccessful && response.body()?.success == true) {
            val videos = response.body()?.data ?: emptyList()
            Result.success(videos)
        } else {
            Result.failure(Exception(response.body()?.message ?: "获取推荐视频失败"))
        }
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 本地数据库操作方法
    suspend fun insertVideoLocal(video: VideoEntity) {
        videoDao.insertVideo(video)
    }

    suspend fun updateVideoLocal(video: VideoEntity) {
        videoDao.updateVideo(video)
    }

    suspend fun deleteVideoLocal(video: VideoEntity) {
        videoDao.deleteVideo(video)
    }

    fun getAllVideosLocal(): Flow<List<VideoEntity>> {
        return videoDao.getAllVideos()
    }

    fun getVideoByIdLocal(id: String): Flow<VideoEntity?> {
        return flow { emit(videoDao.getVideoById(id)) }
    }

    fun getVideosByCategoryLocal(category: String): Flow<List<VideoEntity>> {
        return videoDao.getVideosByCategory(category)
    }

    fun searchVideosLocal(query: String): Flow<List<VideoEntity>> {
        return videoDao.searchVideos(query)
    }
}

// 首页数据模型
data class HomeData(
    val bannerItems: List<BannerItem>,
    val movieVideos: List<Video>,
    val tvVideos: List<Video>,
    val animeVideos: List<Video>,
    val varietyVideos: List<Video>,
    val documentaryVideos: List<Video>,
    val shortVideos: List<Video>
)

// 分类模型
data class VideoCategory(
    val id: Int,
    val name: String
) 