package com.mv.app.ui.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mv.app.data.model.BannerItem
import com.mv.app.data.model.Video
import com.mv.app.data.repository.HomeData
import com.mv.app.data.repository.VideoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val repository: VideoRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()

    init {
        loadHomeData()
    }

    private fun loadHomeData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                val result = repository.getRecommendVideos()
                result.fold(
                    onSuccess = { videos ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            movieVideos = videos.filter { it.typeId == 1 },
                            tvVideos = videos.filter { it.typeId == 2 },
                            error = null
                        )
                    },
                    onFailure = { exception ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = exception.message ?: "加载数据失败"
                        )
                    }
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载数据失败"
                )
            }
        }
    }

    fun refresh() {
        loadHomeData()
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class HomeUiState(
    val isLoading: Boolean = true,
    val bannerItems: List<BannerItem> = emptyList(),
    val movieVideos: List<Video> = emptyList(),
    val tvVideos: List<Video> = emptyList(),
    val animeVideos: List<Video> = emptyList(),
    val varietyVideos: List<Video> = emptyList(),
    val documentaryVideos: List<Video> = emptyList(),
    val shortVideos: List<Video> = emptyList(),
    val error: String? = null
) 