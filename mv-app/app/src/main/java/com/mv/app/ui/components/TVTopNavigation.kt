package com.mv.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.*
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay

@Composable
fun TVTopNavigation(
    selectedTabIndex: Int,
    onTabSelected: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    val tabs = listOf(
        "Search" to "搜索",
        "Home" to "首页", 
        "Movies" to "电影",
        "TV" to "电视剧",
        "Anime" to "动漫",
        "Variety" to "综艺",
        "Short" to "短剧",
        "Documentary" to "纪录片",
        "Library" to "我的"
    )
    
    // 为每个标签页创建焦点请求器
    val focusRequesters = remember { List(tabs.size) { FocusRequester() } }
    val focusManager = LocalFocusManager.current
    var currentFocusIndex by remember { mutableIntStateOf(selectedTabIndex) }
    
    // 确保初始焦点 - 使用延迟确保组件完全渲染后再请求焦点
    LaunchedEffect(Unit) {
        delay(100) // 短暂延迟确保组件完全渲染
        if (selectedTabIndex in focusRequesters.indices) {
            focusRequesters[selectedTabIndex].requestFocus()
            currentFocusIndex = selectedTabIndex
        }
    }
    
    // 当选中的标签页改变时，请求焦点
    LaunchedEffect(selectedTabIndex) {
        if (selectedTabIndex in focusRequesters.indices && selectedTabIndex != currentFocusIndex) {
            focusRequesters[selectedTabIndex].requestFocus()
            currentFocusIndex = selectedTabIndex
        }
    }
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = MaterialTheme.colorScheme.background.copy(alpha = 0.95f)
            )
            .padding(horizontal = 48.dp, vertical = 16.dp),
        color = MaterialTheme.colorScheme.background.copy(alpha = 0.95f)
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(24.dp),
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp)
        ) {
            tabs.forEachIndexed { index, (english, chinese) ->
                TVNavigationTab(
                    title = chinese,
                    selected = selectedTabIndex == index,
                    onClick = { 
                        onTabSelected(index)
                        currentFocusIndex = index
                    },
                    focusRequester = focusRequesters[index],
                    onKeyEvent = { keyEvent ->
                        when {
                            keyEvent.key == Key.DirectionRight && keyEvent.type == KeyEventType.KeyDown -> {
                                val nextIndex = (index + 1) % tabs.size
                                focusRequesters[nextIndex].requestFocus()
                                currentFocusIndex = nextIndex
                                true
                            }
                            keyEvent.key == Key.DirectionLeft && keyEvent.type == KeyEventType.KeyDown -> {
                                val prevIndex = if (index == 0) tabs.size - 1 else index - 1
                                focusRequesters[prevIndex].requestFocus()
                                currentFocusIndex = prevIndex
                                true
                            }
                            keyEvent.key == Key.DirectionDown && keyEvent.type == KeyEventType.KeyDown -> {
                                focusManager.moveFocus(androidx.compose.ui.focus.FocusDirection.Down)
                                true
                            }
                            keyEvent.key == Key.DirectionUp && keyEvent.type == KeyEventType.KeyDown -> {
                                // 如果用户按上键，保持在导航栏
                                true
                            }
                            keyEvent.key == Key.Enter && keyEvent.type == KeyEventType.KeyDown -> {
                                onTabSelected(index)
                                currentFocusIndex = index
                                true
                            }
                            keyEvent.key == Key.DirectionCenter && keyEvent.type == KeyEventType.KeyDown -> {
                                onTabSelected(index)
                                currentFocusIndex = index
                                true
                            }
                            else -> false
                        }
                    },
                    modifier = Modifier.focusable()
                )
            }
        }
    }
}

@Composable
private fun TVNavigationTab(
    title: String,
    selected: Boolean,
    onClick: () -> Unit,
    focusRequester: FocusRequester,
    onKeyEvent: (KeyEvent) -> Boolean,
    modifier: Modifier = Modifier
) {
    var isFocused by remember { mutableStateOf(false) }
    
    Surface(
        onClick = onClick,
        modifier = modifier
            .focusRequester(focusRequester)
            .onFocusChanged { 
                isFocused = it.isFocused
            }
            .onKeyEvent(onKeyEvent)
            .clip(RoundedCornerShape(8.dp))
            .padding(horizontal = 4.dp, vertical = 2.dp),
        color = when {
            selected && isFocused -> MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
            selected -> MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
            isFocused -> MaterialTheme.colorScheme.surface.copy(alpha = 0.8f)
            else -> Color.Transparent
        },
        contentColor = when {
            selected -> MaterialTheme.colorScheme.primary
            isFocused -> MaterialTheme.colorScheme.onSurface
            else -> MaterialTheme.colorScheme.onSurfaceVariant
        },
        tonalElevation = if (isFocused) 4.dp else 0.dp
    ) {
        Text(
            text = title,
            fontSize = 18.sp,
            fontWeight = if (selected) FontWeight.Bold else FontWeight.Normal,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp)
        )
    }
}