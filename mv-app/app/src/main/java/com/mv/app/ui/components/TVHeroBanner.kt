package com.mv.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.mv.app.data.model.BannerItem
import kotlinx.coroutines.delay

@Composable
fun TVHeroBanner(
    bannerVideos: List<BannerItem>,
    onVideoClick: (BannerItem) -> Unit,
    modifier: Modifier = Modifier
) {
    var currentIndex by remember { mutableIntStateOf(0) }
    
    // 自动轮播
    LaunchedEffect(bannerVideos) {
        if (bannerVideos.isNotEmpty()) {
            while (true) {
                delay(5000) // 5秒轮播
                currentIndex = (currentIndex + 1) % bannerVideos.size
            }
        }
    }
    
    if (bannerVideos.isEmpty()) {
        HeroBannerSkeleton(modifier = modifier)
        return
    }
    
    val currentVideo = bannerVideos[currentIndex]
    
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(540.dp)
    ) {
        // 背景图片
        AsyncImage(
            model = getHeroImageUrl(currentVideo.backdropPath),
            contentDescription = currentVideo.title,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
        
        // 渐变遮罩
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            Color.Black.copy(alpha = 0.8f),
                            Color.Transparent,
                            Color.Black.copy(alpha = 0.6f)
                        )
                    )
                )
        )
        
        // 内容信息
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(48.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧内容信息
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(end = 32.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 分类标签
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    currentVideo.typeId?.let { videoType ->
                        CategoryChip(text = getTypeText(videoType))
                    }
                    currentVideo.year?.let { year ->
                        CategoryChip(text = year.toString())
                    }
                    currentVideo.area?.let { area ->
                        CategoryChip(text = area)
                    }
                }
                
                // 标题
                Text(
                    text = currentVideo.title ?: "未知标题",
                    fontSize = 48.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                
                // 描述
                Text(
                    text = currentVideo.remark ?: "暂无简介",
                    fontSize = 18.sp,
                    color = Color.White.copy(alpha = 0.9f),
                    maxLines = 3,
                    overflow = TextOverflow.Ellipsis,
                    lineHeight = 24.sp
                )
                
                // 播放按钮
                TVPlayButton(
                    onClick = { onVideoClick(currentVideo) },
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }
        
        // 轮播指示器
        if (bannerVideos.size > 1) {
            Row(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 24.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                bannerVideos.forEachIndexed { index, _ ->
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .clip(RoundedCornerShape(4.dp))
                            .background(
                                if (index == currentIndex) 
                                    Color.White 
                                else 
                                    Color.White.copy(alpha = 0.4f)
                            )
                    )
                }
            }
        }
    }
}

@Composable
private fun TVPlayButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var isFocused by remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    
    Button(
        onClick = onClick,
        modifier = modifier
            .focusRequester(focusRequester)
            .onFocusChanged { isFocused = it.isFocused }
            .height(56.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isFocused) Color.White else Color.White.copy(alpha = 0.9f),
            contentColor = Color.Black
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Icon(
            imageVector = Icons.Default.PlayArrow,
            contentDescription = "播放",
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = "立即播放",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun CategoryChip(
    text: String,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier,
        color = Color.White.copy(alpha = 0.2f),
        shape = RoundedCornerShape(4.dp)
    ) {
        Text(
            text = text,
            fontSize = 14.sp,
            color = Color.White,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}

@Composable
private fun HeroBannerSkeleton(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(540.dp)
            .background(MaterialTheme.colorScheme.surfaceVariant)
    )
}

private fun getHeroImageUrl(pic: String?): String {
    return when {
        pic.isNullOrBlank() -> "https://img.jukuku.top/default.jpg"
        pic.startsWith("http://") || pic.startsWith("https://") -> pic
        else -> "https://image.tmdb.org/t/p/original$pic"
    }
}

private fun getTypeText(type: Int): String {
    return when (type) {
        1 -> "电影"
        2 -> "电视剧"
        3 -> "动漫"
        4 -> "综艺"
        5 -> "纪录片"
        6 -> "短剧"
        else -> "视频"
    }
} 