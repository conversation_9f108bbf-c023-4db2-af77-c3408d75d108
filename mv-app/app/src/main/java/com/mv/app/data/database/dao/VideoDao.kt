package com.mv.app.data.database.dao

import androidx.room.*
import com.mv.app.data.database.entity.VideoEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface VideoDao {
    
    @Query("SELECT * FROM videos ORDER BY createdAt DESC")
    fun getAllVideos(): Flow<List<VideoEntity>>
    
    @Query("SELECT * FROM videos WHERE id = :videoId")
    fun getVideoById(videoId: String): VideoEntity?
    
    @Query("SELECT * FROM videos WHERE category = :category ORDER BY createdAt DESC")
    fun getVideosByCategory(category: String): Flow<List<VideoEntity>>
    
    @Query("SELECT * FROM videos WHERE title LIKE '%' || :query || '%' OR description LIKE '%' || :query || '%'")
    fun searchVideos(query: String): Flow<List<VideoEntity>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertVideo(video: VideoEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertVideos(videos: List<VideoEntity>)
    
    @Update
    fun updateVideo(video: VideoEntity)
    
    @Delete
    fun deleteVideo(video: VideoEntity)
    
    @Query("DELETE FROM videos")
    fun deleteAllVideos()
} 