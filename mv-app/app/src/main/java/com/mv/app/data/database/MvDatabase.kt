package com.mv.app.data.database

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.mv.app.data.database.dao.VideoDao
import com.mv.app.data.database.dao.UserDao
import com.mv.app.data.database.dao.WatchHistoryDao
import com.mv.app.data.database.dao.FavoriteDao
import com.mv.app.data.database.entity.VideoEntity
import com.mv.app.data.database.entity.UserEntity
import com.mv.app.data.database.entity.WatchHistoryEntity
import com.mv.app.data.database.entity.FavoriteEntity
import com.mv.app.data.database.converter.Converters

@Database(
    entities = [
        VideoEntity::class,
        UserEntity::class,
        WatchHistoryEntity::class,
        FavoriteEntity::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class MvDatabase : RoomDatabase() {
    
    abstract fun videoDao(): VideoDao
    abstract fun userDao(): UserDao
    abstract fun watchHistoryDao(): WatchHistoryDao
    abstract fun favoriteDao(): FavoriteDao
} 