package com.mv.app.ui.components

import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyHorizontalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.mv.app.data.model.Video

@Composable
fun TVVideoRow(
    title: String,
    videos: List<Video>,
    onVideoClick: (Video) -> Unit,
    onMoreClick: (() -> Unit)? = null,
    onNavigateToSidebar: (() -> Unit)? = null, // 新增：导航到侧边栏的回调
    twoRows: Boolean = false, // 新增：是否显示两行
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题栏
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 48.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            onMoreClick?.let { onClick ->
                TVMoreButton(onClick = onClick)
            }
        }
        
        // 视频列表
        if (videos.isEmpty()) {
            TVVideoRowSkeleton(twoRows = twoRows)
        } else {
            if (twoRows) {
                // 两行网格布局
                LazyHorizontalGrid(
                    rows = GridCells.Fixed(2),
                    contentPadding = PaddingValues(horizontal = 48.dp),
                    horizontalArrangement = Arrangement.spacedBy(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                    modifier = Modifier.height(580.dp) // 两行卡片的高度 (270dp * 2 + 间距)
                ) {
                    items(videos) { video ->
                        val videoIndex = videos.indexOf(video)
                        val isFirstInRow = videoIndex % 2 == 0 && videoIndex < 2 // 前两个视频（第一列的两个）
                        TVVideoCard(
                            video = video,
                            onClick = onVideoClick,
                            modifier = Modifier.width(180.dp),
                            isFirstInRow = isFirstInRow,
                            onNavigateToSidebar = onNavigateToSidebar
                        )
                    }
                }
            } else {
                // 单行布局（原有逻辑）
                LazyRow(
                    contentPadding = PaddingValues(horizontal = 48.dp),
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    items(videos) { video ->
                        val isFirstInRow = videos.indexOf(video) == 0
                        TVVideoCard(
                            video = video,
                            onClick = onVideoClick,
                            modifier = Modifier.width(180.dp),
                            isFirstInRow = isFirstInRow,
                            onNavigateToSidebar = onNavigateToSidebar
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TVMoreButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var isFocused by remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    
    TextButton(
        onClick = onClick,
        modifier = modifier
            .focusRequester(focusRequester)
            .onFocusChanged { isFocused = it.isFocused },
        colors = ButtonDefaults.textButtonColors(
            contentColor = if (isFocused) 
                MaterialTheme.colorScheme.primary 
            else 
                MaterialTheme.colorScheme.onSurfaceVariant
        )
    ) {
        Text(
            text = "查看更多",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.width(4.dp))
        Icon(
            imageVector = Icons.Default.KeyboardArrowRight,
            contentDescription = "查看更多",
            modifier = Modifier.size(20.dp)
        )
    }
}

@Composable
private fun TVVideoRowSkeleton(
    twoRows: Boolean = false,
    modifier: Modifier = Modifier
) {
    if (twoRows) {
        // 两行骨架屏
        LazyHorizontalGrid(
            rows = GridCells.Fixed(2),
            contentPadding = PaddingValues(horizontal = 48.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            modifier = modifier.height(580.dp)
        ) {
            items(12) { // 显示12个骨架卡片（6列 x 2行）
                TVVideoCardSkeleton()
            }
        }
    } else {
        // 单行骨架屏（原有逻辑）
        LazyRow(
            contentPadding = PaddingValues(horizontal = 48.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            modifier = modifier
        ) {
            items(6) {
                TVVideoCardSkeleton()
            }
        }
    }
}

@Composable
private fun TVVideoCardSkeleton() {
    Card(
        modifier = Modifier
            .width(180.dp)
            .height(270.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column {
            // 图片占位
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(240.dp)
            )
            
            // 文字占位
            Column(
                modifier = Modifier.padding(12.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(14.dp)
                )
                Box(
                    modifier = Modifier
                        .fillMaxWidth(0.7f)
                        .height(12.dp)
                )
            }
        }
    }
} 