package com.mv.app.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

data class Video(
    val id: Int,
    val title: String?,
    val pic: String?,
    val year: Int?,
    val content: String?,
    val director: String?,
    val starring: String?,
    val area: String?,
    val language: String?,
    val typeId: Int?,
    val remark: String?,
    val subTitle: String?,
    val tags: String?,
    val createTime: String?,
    val updateTime: String?,
    val doubanId: String?,
    val imdbId: String?,
    val tmdbId: Int?,
    val backdropPath: String?,
    
    // 详情页额外字段
    val playListByLine: Map<String, List<PlayListItem>>? = null,
    val urls: List<VodUrl>? = null,
)

data class PlayListItem(
    val id: Int,
    val vodInfoId: Int?,
    val lineId: Int?,
    val episode: Int?,
    val url: String?
)

data class VodUrl(
    val id: Int,
    val cloudId: Int?,
    val url: String?,
    val vodInfoId: Int?,
    val checkStatus: Int?,
    val isInvalid: Int?,
    val pwd: String?,
    val stoken: String?
)

// API响应包装类
data class VideoListResponse(
    val success: Boolean,
    val data: VideoListData?,
    val message: String?
)

data class VideoListData(
    val items: List<Video>,
    val pagination: Pagination
)

data class VideoDetailResponse(
    val success: Boolean,
    val data: Video?,
    val message: String?
)

data class HomeDataResponse(
    val success: Boolean,
    val data: HomeData?,
    val message: String?
)

data class HomeData(
    val bannerVideos: List<BannerItem>?,
    val movieVideos: List<Video>?,
    val tvVideos: List<Video>?,
    val animeVideos: List<Video>?,
    val varietyVideos: List<Video>?,
    val documentaryVideos: List<Video>?,
    val shortVideos: List<Video>?
)

data class BannerItem(
    val id: Int,
    val title: String,
    val pic: String?,
    val backdropPath: String?,
    val typeId: Int?,
    val rating: Double?,
    val year: Int?,
    val area: String?,
    val director: String?,
    val starring: String?,
    val language: String?,
    val remark: String?
)

data class Pagination(
    val page: Int,
    val pageSize: Int,
    val total: Int,
    val totalPages: Int
) 

data class RecommendVideosResponse(
    val success: Boolean,
    val data: List<Video>?,
    val message: String?
)