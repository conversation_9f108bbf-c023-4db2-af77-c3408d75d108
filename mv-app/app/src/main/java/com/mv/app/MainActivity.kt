package com.mv.app

import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.platform.LocalFocusManager
import androidx.navigation.compose.rememberNavController
import com.mv.app.ui.components.TVSideNavigation
import com.mv.app.ui.components.TVFocusManager
import com.mv.app.ui.components.EnsureInitialFocus
import com.mv.app.ui.navigation.MvNavigation
import com.mv.app.ui.theme.MvAndroidTVTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collect

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            MvAndroidTVTheme {
                MvApp()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MvApp() {
    val navController = rememberNavController()
    var selectedTabIndex by remember { mutableIntStateOf(1) } // 默认选中首页
    val sideNavigationFocusRequester = remember { FocusRequester() }
    var isNavigatingToSidebar by remember { mutableStateOf(false) } // 标记是否正在导航到侧边栏
    var isNavigatingToDetail by remember { mutableStateOf(false) } // 标记是否正在导航到详情页
    var isHandlingBackKey by remember { mutableStateOf(false) } // 标记是否正在处理返回键

    // 监听导航状态，当离开详情页时重置标记
    LaunchedEffect(navController) {
        navController.currentBackStackEntryFlow.collect { backStackEntry ->
            val currentRoute = backStackEntry.destination.route ?: ""
            Log.d("MainActivity", "导航状态变化，当前路由: $currentRoute, isNavigatingToDetail: $isNavigatingToDetail, isHandlingBackKey: $isHandlingBackKey")

            if (!currentRoute.startsWith("detail/") && isNavigatingToDetail) {
                Log.d("MainActivity", "离开详情页，重置导航标记")
                isNavigatingToDetail = false
                // 同时重置返回键处理标记
                if (isHandlingBackKey) {
                    Log.d("MainActivity", "重置处理返回键标记")
                    isHandlingBackKey = false
                }
            }

            // 如果当前在详情页，确保标记为true
            if (currentRoute.startsWith("detail/") && !isNavigatingToDetail) {
                Log.d("MainActivity", "进入详情页，设置导航标记")
                isNavigatingToDetail = true
            }
        }
    }
    
    // 使用强化的焦点管理器确保初始焦点
    EnsureInitialFocus(
        focusRequester = sideNavigationFocusRequester,
        enabled = true,
        delayMs = 300
    )
    
    // 导航函数
    fun navigateToPage(index: Int) {
        Log.d("MainActivity", "导航到页面索引: $index")
        when (index) {
            0 -> {
                Log.d("MainActivity", "导航到搜索页面")
                navController.navigate("search") {
                    popUpTo("home") { inclusive = false }
                    launchSingleTop = true
                }
            }
            1 -> {
                Log.d("MainActivity", "导航到首页")
                navController.navigate("home") {
                    popUpTo("home") { inclusive = true }
                    launchSingleTop = true
                }
            }
            2 -> {
                Log.d("MainActivity", "导航到电影分类")
                navController.navigate("category/movies")
            }
            3 -> {
                Log.d("MainActivity", "导航到电视剧分类")
                navController.navigate("category/tv")
            }
            4 -> {
                Log.d("MainActivity", "导航到动漫分类")
                navController.navigate("category/anime")
            }
            5 -> {
                Log.d("MainActivity", "导航到综艺分类")
                navController.navigate("category/variety")
            }
            6 -> {
                Log.d("MainActivity", "导航到短剧分类")
                navController.navigate("category/short")
            }
            7 -> {
                Log.d("MainActivity", "导航到纪录片分类")
                navController.navigate("category/documentary")
            }
            8 -> {
                Log.d("MainActivity", "导航到个人页面")
                navController.navigate("profile") {
                    popUpTo("home") { inclusive = false }
                    launchSingleTop = true
                }
            }
        }
    }
    
    Row(
        modifier = Modifier
            .fillMaxSize()
            .onKeyEvent { keyEvent ->
                when {
                    // 返回键处理
                    keyEvent.key == Key.Back && keyEvent.type == KeyEventType.KeyDown -> {
                        val currentRoute = navController.currentDestination?.route ?: ""
                        Log.d("MainActivity", "全局返回键处理，当前路由: $currentRoute")

                        if (currentRoute.startsWith("detail/")) {
                            Log.d("MainActivity", "在详情页按返回键，设置处理标记并执行返回")
                            isHandlingBackKey = true
                            navController.popBackStack()
                            true
                        } else {
                            false
                        }
                    }
                    // Menu键可以回到左侧导航栏
                    keyEvent.key == Key.Menu && keyEvent.type == KeyEventType.KeyDown -> {
                        sideNavigationFocusRequester.requestFocus()
                        true
                    }
                    else -> false
                }
            }
    ) {
        // 左侧导航栏
        TVSideNavigation(
            selectedTabIndex = selectedTabIndex,
            onTabSelected = { index ->
                selectedTabIndex = index
                navigateToPage(index)
            },
            onTabFocused = { index ->
                // 焦点切换时自动导航，但如果是从主内容区域返回、正在导航到详情页或正在处理返回键则不导航
                if (!isNavigatingToSidebar && !isNavigatingToDetail && !isHandlingBackKey) {
                    selectedTabIndex = index
                    navigateToPage(index)
                } else {
                    // 重置标记
                    isNavigatingToSidebar = false
                    if (isNavigatingToDetail) {
                        Log.d("MainActivity", "正在导航到详情页，跳过侧边栏导航")
                    }
                    if (isHandlingBackKey) {
                        Log.d("MainActivity", "正在处理返回键，跳过侧边栏导航")
                    }
                }
            },
            modifier = Modifier.focusRequester(sideNavigationFocusRequester)
        )
        
        // 主内容区域
        Box(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
        ) {
            MvNavigation(
                navController = navController,
                onNavigateToSidebar = {
                    // 请求焦点到侧边栏
                    Log.d("MainActivity", "请求焦点到侧边栏，当前选中索引: $selectedTabIndex")
                    // 先更新selectedTabIndex，确保它与当前页面匹配
                    val currentRoute = navController.currentDestination?.route ?: ""
                    Log.d("MainActivity", "当前路由: $currentRoute")

                    // 设置标记，表示正在从主内容区域导航到侧边栏
                    isNavigatingToSidebar = true

                    // 根据当前路由更新selectedTabIndex
                    when {
                        currentRoute == "search" -> selectedTabIndex = 0
                        currentRoute == "home" -> selectedTabIndex = 1
                        currentRoute.startsWith("category/movies") -> selectedTabIndex = 2
                        currentRoute.startsWith("category/tv") -> selectedTabIndex = 3
                        currentRoute.startsWith("category/anime") -> selectedTabIndex = 4
                        currentRoute.startsWith("category/variety") -> selectedTabIndex = 5
                        currentRoute.startsWith("category/short") -> selectedTabIndex = 6
                        currentRoute.startsWith("category/documentary") -> selectedTabIndex = 7
                        currentRoute == "profile" -> selectedTabIndex = 8
                    }

                    // 然后请求焦点
                    sideNavigationFocusRequester.requestFocus()
                },
                onNavigateToDetail = {
                    // 设置标记，表示正在导航到详情页
                    Log.d("MainActivity", "设置导航到详情页标记")
                    isNavigatingToDetail = true
                },
                onHandleBackKey = {
                    // 设置标记，表示正在处理返回键
                    Log.d("MainActivity", "设置处理返回键标记")
                    isHandlingBackKey = true
                }
            )
        }
    }
} 