package com.mv.app.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// TV专用颜色
val TVPrimary = Color(0xFF1976D2)
val TVSecondary = Color(0xFF424242)
val TVAccent = Color(0xFFFF5722)
val TVBackground = Color(0xFF000000)
val TVSurface = Color(0xFF1C1C1C)
val TVOnPrimary = Color.White
val TVOnSecondary = Color.White
val TVOnBackground = Color.White
val TVOnSurface = Color.White

// 视频相关颜色
val VideoBackground = Color(0xFF000000)
val VideoControlBackground = Color(0x88000000)
val VideoProgressBackground = Color(0x44FFFFFF)
val VideoProgressForeground = Color(0xFFFF5722) 