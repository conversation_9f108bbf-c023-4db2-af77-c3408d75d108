package com.mv.app.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class User(
    val id: String,
    val username: String,
    val email: String,
    val avatar: String,
    val cloudAccounts: List<CloudAccount>,
    val preferences: UserPreferences,
    val createdAt: Long,
    val updatedAt: Long
) : Parcelable

@Parcelize
data class CloudAccount(
    val id: String,
    val provider: CloudProvider,
    val accountName: String,
    val accessToken: String,
    val refreshToken: String,
    val expiresAt: Long,
    val isActive: Boolean,
    val createdAt: Long,
    val updatedAt: Long
) : Parcelable

@Parcelize
data class UserPreferences(
    val autoPlay: Boolean = true,
    val defaultQuality: VideoQuality = VideoQuality.HD,
    val preferredCloudProvider: CloudProvider? = null,
    val playbackSpeed: Float = 1.0f,
    val subtitleEnabled: Boolean = true,
    val darkTheme: Boolean = true
) : Parcelable

@Parcelize
data class WatchHistory(
    val id: String,
    val userId: String,
    val videoId: String,
    val episodeId: String?,
    val watchedAt: Long,
    val progress: Long, // 观看进度（毫秒）
    val duration: Long  // 视频总时长（毫秒）
) : Parcelable

@Parcelize
data class Favorite(
    val id: String,
    val userId: String,
    val videoId: String,
    val createdAt: Long
) : Parcelable 