package com.mv.app.data.api

import com.mv.app.data.model.*
import retrofit2.Response
import retrofit2.http.*

interface MvApiService {
    
    /**
     * 获取视频列表
     * @param page 页码，默认为1
     * @param pageSize 每页大小，默认为20
     * @param typeId 类型ID (1:电影, 2:剧集, 3:动漫, 4:综艺, 5:纪录片, 7:短剧)
     * @param area 地区
     * @param year 年份
     * @param director 导演
     * @param starring 主演
     * @param language 语言
     */
    @GET("videos")
    suspend fun getVideoList(
        @Query("page") page: Int = 1,
        @Query("pageSize") pageSize: Int = 20,
        @Query("typeId") typeId: Int? = null,
        @Query("area") area: String? = null,
        @Query("year") year: Int? = null,
        @Query("director") director: String? = null,
        @Query("starring") starring: String? = null,
        @Query("language") language: String? = null
    ): Response<VideoListResponse>

    /**
     * 根据ID获取视频详情
     */
    @GET("videos/{id}")
    suspend fun getVideoDetail(
        @Path("id") id: Int
    ): Response<VideoDetailResponse>

    /**
     * 搜索视频
     * @param query 搜索关键词
     * @param page 页码，默认为1
     * @param pageSize 每页大小，默认为20
     */
    @GET("videos/search")
    suspend fun searchVideos(
        @Query("q") query: String,
        @Query("page") page: Int = 1,
        @Query("pageSize") pageSize: Int = 20
    ): Response<VideoListResponse>

    /**
     * 获取轮播图数据
     */
    @GET("videos/getHomeData") 
    suspend fun getHomeData(): Response<HomeDataResponse>

    /**
     * 获得推荐视频
     */
    @GET("videos/recommend")
    suspend fun recommend(): Response<RecommendVideosResponse>
} 