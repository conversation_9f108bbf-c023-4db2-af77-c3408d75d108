package com.mv.app.ui.detail

import android.util.Log
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.input.key.*
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.mv.app.ui.navigation.MvDestination
import com.mv.app.ui.player.components.PlaylistSelector

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DetailScreen(
    navController: NavController,
    videoId: String,
    onHandleBackKey: (() -> Unit)? = null,
    viewModel: DetailViewModel = hiltViewModel()
) {
    Log.d("DetailScreen", "DetailScreen 被创建，videoId: $videoId")
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(videoId) {
        Log.d("DetailScreen", "开始加载视频详情，videoId: $videoId")
        viewModel.loadVideoDetail(videoId.toInt())
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .onKeyEvent { keyEvent ->
                when {
                    keyEvent.key == Key.Back && keyEvent.type == KeyEventType.KeyDown -> {
                        Log.d("DetailScreen", "返回键被按下，执行返回操作")
                        onHandleBackKey?.invoke() // 通知MainActivity正在处理返回键
                        navController.popBackStack()
                        true
                    }
                    else -> false
                }
            }
            .focusable()
    ) {
        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            
            uiState.video != null -> {
                VideoDetailContent(
                    video = uiState.video!!,
                    onPlayClick = { lineId, episodeId ->
                        navController.navigate(
                            MvDestination.Player.createRoute(videoId, episodeId)
                        )
                    }
                )
            }
            
            else -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "视频不存在",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Text(
                            text = "请检查视频链接",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                }
            }
        }
        
        // 错误处理
        uiState.error?.let { error ->
            LaunchedEffect(error) {
                viewModel.clearError()
            }
        }
    }
}

@Composable
private fun VideoDetailContent(
    video: com.mv.app.data.model.Video,
    onPlayClick: (String, String) -> Unit
) {
    var isFavorite by remember { mutableStateOf(false) }
    var selectedLineId by remember { mutableStateOf<String?>(null) }
    var selectedEpisodeId by remember { mutableStateOf<String?>(null) }
    
    // 设置默认选择
    LaunchedEffect(video.playListByLine) {
        if (selectedLineId == null && !video.playListByLine.isNullOrEmpty()) {
            val firstLine = video.playListByLine.keys.firstOrNull()
            selectedLineId = firstLine
            if (firstLine != null) {
                val firstEpisode = video.playListByLine[firstLine]?.firstOrNull()
                selectedEpisodeId = firstEpisode?.id?.toString()
            }
        }
    }
    
    val listState = rememberLazyListState()
    
    LazyColumn(
        state = listState,
        modifier = Modifier
            .fillMaxSize()
            .onKeyEvent { keyEvent ->
                when {
                    keyEvent.key == Key.DirectionUp && keyEvent.type == KeyEventType.KeyDown -> {
                        if (listState.firstVisibleItemIndex == 0 && listState.firstVisibleItemScrollOffset == 0) {
                            // 在顶部，不消费事件，让全局处理器处理
                            false
                        } else {
                            // 不在顶部，让LazyColumn处理滚动
                            false
                        }
                    }
                    else -> false
                }
            },
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // 视频封面和基本信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 封面
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(video.pic)
                        .crossfade(true)
                        .build(),
                    contentDescription = video.title,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .width(150.dp)
                        .height(200.dp)
                        .clip(RoundedCornerShape(8.dp))
                )
                
                // 视频信息
                Column(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = video.title ?: "未知标题",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                    
                    video.year?.let {
                        Text(
                            text = "年份: $it",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                    
                    video.area?.let {
                        Text(
                            text = "地区: $it",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                    
                    video.director?.let {
                        Text(
                            text = "导演: $it",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                    
                    video.starring?.let {
                        Text(
                            text = "主演: $it",
                            style = MaterialTheme.typography.bodyMedium,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                    
                    video.remark?.let {
                        Text(
                            text = "状态: $it",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }
        
        item {
            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Button(
                    onClick = { 
                        val lineId = selectedLineId ?: "0"
                        val episodeId = selectedEpisodeId ?: "default"
                        onPlayClick(lineId, episodeId) 
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("播放")
                }
                
                OutlinedButton(
                    onClick = { isFavorite = !isFavorite },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = if (isFavorite) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(if (isFavorite) "已收藏" else "收藏")
                }
            }
        }
        
        item {
            // 剧情简介
            video.content?.let { content ->
                if (content.isNotEmpty()) {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "剧情简介",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Text(
                                text = content,
                                style = MaterialTheme.typography.bodyMedium,
                                lineHeight = MaterialTheme.typography.bodyMedium.lineHeight
                            )
                        }
                    }
                }
            }
        }
        
        item {
            // 播放列表
            video.playListByLine?.let { playListByLine ->
                if (playListByLine.isNotEmpty()) {
                    PlaylistSelector(
                        playListByLine = playListByLine,
                        selectedLineId = selectedLineId,
                        selectedEpisodeId = selectedEpisodeId,
                        onLineSelected = { lineId ->
                            selectedLineId = lineId
                            // 自动选择第一集
                            val firstEpisode = playListByLine[lineId]?.firstOrNull()
                            selectedEpisodeId = firstEpisode?.id?.toString()
                        },
                        onEpisodeSelected = { episodeId ->
                            selectedEpisodeId = episodeId
                        }
                    )
                }
            }
        }
    }
} 