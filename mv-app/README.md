# MV Android TV 应用

这是一个Android TV视频播放应用，支持多种网盘登录和视频播放功能。

## 项目状态

✅ **编译成功** - 项目已经可以成功编译并生成APK文件
✅ **UI升级完成** - 全新的现代化TV流媒体界面

## 主要功能

1. **我的页面** - 支持9大网盘登录
   - 阿里云盘
   - 夸克网盘
   - UC网盘
   - 百度网盘
   - 迅雷网盘
   - 123云盘
   - 115网盘
   - 移动云盘
   - 天翼云盘

2. **首页** - 海报轮播栏和各类最新内容
3. **分类页** - 按年份和地区筛选的视频分类
4. **搜索页** - 视频搜索功能
5. **详情页/播放页** - 使用Media3播放器，支持在线链接和网盘播放

## 技术栈

- **Android TV SDK** - TV专用组件
- **Jetpack Compose** - 现代化UI开发
- **Media3** - 视频播放引擎
- **Retrofit** - 网络请求
- **Room** - 本地数据库
- **Hilt** - 依赖注入
- **Coil** - 图片加载

## 项目结构

```
app/src/main/java/com/mv/app/
├── data/
│   ├── api/           # API接口和响应模型
│   ├── database/      # Room数据库相关
│   └── model/         # 数据模型
├── di/                # 依赖注入模块
├── ui/                # UI界面
│   ├── category/      # 分类页面
│   ├── detail/        # 详情页面
│   ├── home/          # 首页
│   ├── navigation/    # 导航
│   ├── player/        # 播放器页面
│   ├── profile/       # 个人页面
│   ├── search/        # 搜索页面
│   └── theme/         # 主题配置
├── MainActivity.kt    # 主Activity
└── MvApplication.kt   # 应用类
```

## 编译要求

- Android API 34
- Kotlin 1.8.10
- Java 17
- Android Gradle Plugin 8.1.4

## 编译命令

```bash
# 编译Debug版本
./gradlew assembleDebug

# 清理项目
./gradlew clean
```

## 当前状态

- ✅ 项目框架搭建完成
- ✅ 所有必要的依赖和配置已添加
- ✅ 数据库模型和API接口已创建
- ✅ 基础UI页面框架已搭建
- ✅ 编译成功，可以生成APK
- ✅ **播放器实现完成** - 集成Media3播放器，支持HLS播放
- ✅ **线路和集数切换** - 支持多线路和多集数切换
- ✅ **播放列表管理** - 完整的播放列表显示和选择功能
- ✅ **现代化TV UI** - 全新的流媒体界面设计
- ✅ **遥控器导航优化** - 完整的焦点导航支持

## 播放器功能

### 核心特性
1. **HLS播放支持** - 使用Media3 ExoPlayer，完美支持HLS流媒体
2. **多线路切换** - 支持在不同播放线路间无缝切换
3. **集数切换** - 支持上一集/下一集快速切换
4. **横竖屏适配** - 自动适配横屏全屏播放和竖屏列表模式
5. **播放控制** - 完整的播放器控制界面

### 技术实现
- **PlayerViewModel** - 管理播放器状态和播放列表数据
- **VideoPlayer组件** - 基于Media3的播放器封装
- **PlaylistSelector组件** - 线路和集数选择界面
- **自动播放** - 根据API的playListByLine数据自动播放

### 数据结构支持
播放器完全支持API返回的playListByLine数据结构：
```json
{
  "playListByLine": {
    "1": [
      {"id": 1, "episode": 1, "url": "https://example.com/episode1.m3u8"},
      {"id": 2, "episode": 2, "url": "https://example.com/episode2.m3u8"}
    ],
    "2": [
      {"id": 3, "episode": 1, "url": "https://example2.com/episode1.m3u8"}
    ]
  }
}
```

### 使用方式
1. 在详情页选择线路和集数
2. 点击播放按钮跳转到播放器
3. 播放器自动加载对应的HLS链接
4. 支持切换线路和集数
5. 横屏时显示全屏播放器
6. 竖屏时显示播放器+信息列表

## UI升级亮点

### 🎨 现代化设计
- 参考Netflix、Disney+等主流平台的设计风格
- 深色主题，适合TV观看环境
- 大型横幅轮播，突出主要内容
- 卡片式内容展示，信息层次清晰

### 🎮 遥控器友好
- 完整的焦点导航支持
- 焦点状态视觉反馈
- 缩放动画效果
- 边框高亮显示

### 📱 组件化设计
- TVTopNavigation - 顶部导航栏
- TVHeroBanner - 大型横幅轮播
- TVVideoCard - TV优化视频卡片
- TVVideoRow - 水平滚动内容行

### 🎯 用户体验优化
- 自动轮播功能
- 骨架屏加载状态
- 错误处理界面
- 搜索功能增强

## 下一步开发

1. 完善网盘登录功能
2. 实现播放历史记录
3. 添加倍速播放功能
4. 实现字幕支持
5. 优化播放器性能
6. 添加断点续播功能

## API配置

当前API基础URL配置为：`