# API 配置说明

## 配置API地址

在运行Android应用之前，您需要配置正确的API地址。

### 步骤1：部署API

1. 进入 `mv-api` 目录
2. 安装依赖：`npm install`
3. 部署到Cloudflare Workers：`npm run deploy`
4. 部署完成后，记录您的API地址，格式类似：`https://mv-api.你的用户名.workers.dev/`

### 步骤2：配置Android应用

1. 打开文件：`mv-app/app/src/main/java/com/mv/app/di/NetworkModule.kt`
2. 找到第32行的 `baseUrl` 配置
3. 将 `https://mv-api.your-username.workers.dev/` 替换为您的实际API地址

```kotlin
fun provideRetrofit(okHttpClient: OkHttpClient): Retrofit {
    return Retrofit.Builder()
        .baseUrl("https://你的实际API地址/")  // 在这里替换
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create())
        .build()
}
```

### 步骤3：测试API连接

1. 确保API正常运行，可以在浏览器中访问：`你的API地址/videos?page=1&pageSize=5`
2. 应该返回JSON格式的视频数据

### API端点说明

本应用使用以下API端点：

- `GET /videos` - 获取视频列表（支持分页和分类过滤）
- `GET /videos/:id` - 获取视频详情
- `GET /videos/search?q=关键词` - 搜索视频
- `GET /videos/getBannerVideos` - 获取轮播图数据

### 数据库配置

API使用MySQL数据库，配置信息在 `mv-api/wrangler.jsonc` 中的 `hyperdrive` 部分。请确保：

1. 数据库连接正常
2. 数据表已创建（参考 `mv-site/docs/vod_db.sql`）
3. 有足够的测试数据

### 常见问题

1. **网络连接失败**：检查API地址是否正确，是否包含尾部斜杠
2. **返回404错误**：确认API已成功部署，路由配置正确
3. **数据为空**：检查数据库连接和数据是否存在

### 本地开发

如果需要本地开发调试：

1. 在 `mv-api` 目录运行：`npm run dev`
2. 将Android应用的API地址配置为：`http://localhost:8787/`
3. 注意：Android模拟器可能需要使用 `http://********:8787/` 访问本地服务 