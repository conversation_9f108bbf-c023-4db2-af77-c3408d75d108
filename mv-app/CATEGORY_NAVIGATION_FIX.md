# 分类导航修复说明

## 问题诊断

用户反馈侧边栏无法切换不同分类页面。经过分析，发现了以下几个可能的问题：

1. **导航配置过于复杂** - 使用了复杂的 `popUpTo` 和 `saveState` 配置
2. **ViewModel作用域问题** - 可能导致同一个ViewModel实例被重用
3. **状态重置问题** - 切换分类时状态没有正确重置

## 修复措施

### 1. 简化导航配置
**文件**: `app/src/main/java/com/mv/app/MainActivity.kt`

移除了复杂的导航配置，改为简单的直接导航：
```kotlin
// 修改前
navController.navigate("category/movies") {
    popUpTo("home") { inclusive = false }
    launchSingleTop = true
}

// 修改后
navController.navigate("category/movies")
```

### 2. 修复ViewModel作用域
**文件**: `app/src/main/java/com/mv/app/ui/category/CategoryScreen.kt`

为每个分类类型创建独立的ViewModel实例：
```kotlin
// 修改前
viewModel: CategoryViewModel = hiltViewModel()

// 修改后
viewModel: CategoryViewModel = hiltViewModel(key = categoryType)
```

### 3. 强制状态重置
**文件**: `app/src/main/java/com/mv/app/ui/category/CategoryViewModel.kt`

在初始化时强制重置状态：
```kotlin
fun initializeWithCategoryType(categoryType: String) {
    // 重置状态
    _uiState.value = CategoryUiState()
    
    // 其他初始化逻辑...
}
```

### 4. 添加调试日志
在关键位置添加了日志输出，便于调试：
- MainActivity导航函数
- CategoryViewModel初始化
- CategoryScreen LaunchedEffect

### 5. 增强视觉反馈
**文件**: `app/src/main/java/com/mv/app/ui/category/CategoryScreen.kt`

改进了分类标题显示，增加了当前分类类型的明确指示：
```kotlin
Text(text = categoryTitle, ...)  // 中文标题
Text(text = "当前分类类型: $categoryType", ...)  // 英文类型
```

## 测试方法

### 1. 编译验证
```bash
./gradlew assembleDebug
```
✅ 编译成功

### 2. 功能测试步骤
1. 启动应用
2. 使用遥控器在侧边栏中导航
3. 依次选择不同的分类项：
   - 电影 (movies)
   - 电视剧 (tv)
   - 动漫 (anime)
   - 综艺 (variety)
   - 短剧 (short)
   - 纪录片 (documentary)
4. 观察页面标题是否正确切换
5. 检查日志输出确认导航和初始化是否正常

### 3. 预期结果
- 每次选择不同分类时，页面标题应该正确显示对应的中文名称
- 页面下方应显示"当前分类类型: xxx"
- 日志中应显示正确的导航和初始化信息

## 调试信息

如果问题仍然存在，可以通过以下日志标签查看调试信息：
- `MainActivity` - 导航操作日志
- `CategoryViewModel` - 分类初始化日志
- `CategoryScreen` - LaunchedEffect触发日志

## 分类映射表

| 侧边栏索引 | 分类名称 | categoryType | typeId |
|-----------|----------|--------------|--------|
| 2 | 电影 | movies | 1 |
| 3 | 电视剧 | tv | 2 |
| 4 | 动漫 | anime | 3 |
| 5 | 综艺 | variety | 4 |
| 6 | 短剧 | short | 7 |
| 7 | 纪录片 | documentary | 5 |

## 后续优化建议

1. **移除调试日志** - 在确认功能正常后移除调试日志
2. **恢复导航优化** - 在基本功能正常后，可以考虑重新添加适当的导航优化
3. **添加动画效果** - 为分类切换添加平滑的过渡动画
4. **性能优化** - 优化ViewModel的创建和销毁机制

## 编译状态

✅ **编译成功** - 所有修改已通过编译测试
✅ **语法正确** - 没有编译错误
✅ **依赖完整** - 所有必要的导入和依赖都正确配置

现在请测试分类导航功能，应该能够正常在不同分类间切换了。
