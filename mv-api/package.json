{"name": "mv-api", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy --minify", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "bun:dev": "bun run src/bun-server.ts", "bun:start": "NODE_ENV=production bun run src/bun-server.ts", "bun:prod": "NODE_ENV=production bun run src/bun-server.ts", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:stop": "pm2 stop mv-api", "pm2:restart": "pm2 restart mv-api", "pm2:logs": "pm2 logs mv-api", "docker:build": "docker build -t mv-api .", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "dependencies": {"drizzle-orm": "^0.44.2", "hono": "^4.7.11", "mysql2": "^3.14.1", "dotenv": "^16.4.5"}, "devDependencies": {"@types/node": "^24.0.3", "drizzle-kit": "^0.31.1", "wrangler": "^4.20.0", "@types/bun": "latest"}}