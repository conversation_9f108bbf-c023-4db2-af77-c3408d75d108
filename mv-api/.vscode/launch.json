{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Server",
            "type": "node",
            "request": "launch",
            "runtimeExecutable": "npm",
            "runtimeArgs": ["run", "dev"],
            "skipFiles": ["<node_internals>/**"],
            "console": "integratedTerminal",
            "sourceMaps": true,
            "outFiles": ["${workspaceFolder}/dist/**/*.js"],
            "env": {
                "NODE_ENV": "development"
            }
        },
        {
            "name": "Attach to Server",
            "type": "node",
            "request": "attach",
            "port": 9229,
            "sourceMaps": true,
            "restart": true,
            "skipFiles": ["<node_internals>/**"],
            
        },
        {
            "name": "单文件调试",
            "type": "node",
            "request": "launch",
            "skipFiles": ["<node_internals>/**"],
            "program": "${file}",
            "outFiles": ["${workspaceFolder}/**/*.js"],
            "sourceMaps": true
        }
    ],
    "compounds": [
        {
            "name": "Launch then Attach",
            "configurations": ["Launch Server", "Attach to Server"]
        }
    ]
}