 #!/bin/bash

# 部署脚本
set -e

echo "🚀 开始部署 MV API..."

# 1. 更新代码
echo "📥 更新代码..."
git pull origin main

# 2. 安装依赖
echo "📦 安装依赖..."
bun install

# 3. 检查环境变量
if [ ! -f .env ]; then
    echo "❌ 错误: .env 文件不存在"
    echo "请复制 .env.example 到 .env 并配置数据库信息"
    exit 1
fi

# 4. 创建必要的目录
echo "📁 创建目录..."
mkdir -p logs
mkdir -p ssl

# 5. 数据库迁移
echo "🗄️  执行数据库迁移..."
bun run db:push

# 6. 构建项目 (如果需要)
echo "🔨 构建项目..."
# 这里可以添加构建步骤

# 7. 重启服务
echo "🔄 重启服务..."
if command -v pm2 &> /dev/null; then
    pm2 restart ecosystem.config.js --env production
elif command -v docker-compose &> /dev/null; then
    docker-compose down
    docker-compose up -d --build
else
    echo "⚠️  警告: 未找到 PM2 或 Docker Compose，请手动重启服务"
fi

# 8. 健康检查
echo "🏥 健康检查..."
sleep 5
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ 部署成功！服务正在运行"
else
    echo "❌ 部署失败！服务未正常启动"
    exit 1
fi

echo "🎉 部署完成！"