module.exports = {
  apps: [{
    name: 'mv-api',
    script: 'bun',
    args: 'run src/bun-server.ts',
    instances: '1', // 单实例
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    // 日志配置
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss',
    
    // 自动重启配置
    watch: false,
    ignore_watch: ['node_modules', 'logs'],
    
    // 内存和CPU限制
    max_memory_restart: '1G',
    
    // 重启策略
    restart_delay: 4000,
    max_restarts: 5,
    min_uptime: '10s'
  }]
}; 