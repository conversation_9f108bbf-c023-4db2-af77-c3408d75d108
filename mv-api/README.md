# MV API

这是一个使用Hono框架和Drizzle ORM的API项目，支持在Cloudflare Workers和Bun环境中运行。

## 技术栈

- [Hono](https://hono.dev/) - 轻量级、快速的Web框架
- [Drizzle ORM](https://orm.drizzle.team/) - TypeScript ORM
- [MySQL2](https://github.com/sidorares/node-mysql2) - MySQL数据库驱动
- [Bun](https://bun.sh/) - 高性能JavaScript运行时
- [Cloudflare Workers](https://workers.cloudflare.com/) - 边缘计算平台（可选）

## 运行环境

### 1. Bun环境（推荐）

#### 安装依赖
```bash
bun install
```

#### 配置环境变量
复制 `.env.example` 到 `.env` 并修改数据库配置：

```bash
cp .env.example .env
```

修改 `.env` 文件中的数据库配置：
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=mv_database
DB_PORT=3306
PORT=3000
```

#### 运行服务器
```bash
# 开发环境
bun run bun:dev

# 生产环境
bun run bun:start
```

#### 数据库管理
```bash
# 生成数据库迁移
bun run db:generate

# 推送schema到数据库
bun run db:push

# 启动数据库管理界面
bun run db:studio
```

### 2. Cloudflare Workers环境

#### 安装依赖
```bash
npm install
```

#### 配置HYPERDRIVE
在`wrangler.jsonc`中配置HYPERDRIVE绑定：

```json
"hyperdrive": [
  {
    "binding": "HYPERDRIVE",
    "id": "your-hyperdrive-id",
    "localConnectionString": "mysql://user:password@host:port/database"
  }
]
```

#### 运行和部署
```bash
# 本地开发
npm run dev

# 部署到Cloudflare
npm run deploy

# 生成类型定义
npm run cf-typegen
```

## 项目结构

```
/
├── src/
│   ├── db/
│   │   ├── schema.ts       # 数据库模式定义
│   │   └── index.ts        # 数据库连接工具
│   ├── routes/
│   │   ├── index.ts        # 路由入口
│   │   └── videos.ts       # 视频相关路由
│   ├── index.ts            # Cloudflare Workers入口
│   └── bun-server.ts       # Bun服务器入口
├── drizzle.config.ts       # Drizzle配置
├── wrangler.jsonc          # Cloudflare Workers配置
├── .env.example            # 环境变量示例
└── README.md
```

## API 端点

- `GET /health` - 健康检查
- `GET /` - 首页
- `GET /api/videos` - 获取视频列表
- `GET /api/videos/:id` - 获取视频详情
- `GET /api/search?q=关键词` - 搜索视频
- `GET /api/home` - 获取首页数据
- `GET /api/banner` - 获取轮播图数据

## 数据库表结构

项目包含以下主要数据表：

- `vod_infos` - 视频信息表
- `vod_play_list` - 视频播放列表
- `vod_urls` - 视频URL信息
- `vod_pic_info` - 视频图片信息
- `cloud_files` - 云存储文件信息
- `recent_hot` - 最近热门视频

## 开发说明

### 数据库操作

所有数据库操作都使用 Drizzle ORM，支持类型安全的查询。

### 环境切换

- **Bun环境**: 使用 `bun run bun:dev` 启动，直连MySQL数据库
- **Cloudflare环境**: 使用 `npm run dev` 启动，通过HYPERDRIVE连接数据库

### 数据库迁移

使用 Drizzle Kit 管理数据库schema：

```bash
# 生成迁移文件
bun run db:generate

# 应用迁移到数据库
bun run db:push

# 启动可视化管理界面
bun run db:studio
```

## 部署

### Bun环境部署

```bash
# 安装依赖
bun install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 启动服务
bun run bun:start
```

### Cloudflare部署

```bash
# 配置wrangler.jsonc
# 部署到Cloudflare
npm run deploy
```

## 注意事项

1. **数据库连接**: Bun环境直接连接MySQL，Cloudflare环境通过HYPERDRIVE连接
2. **环境变量**: 不同环境使用不同的配置方式
3. **性能**: Bun环境提供更好的本地开发体验，Cloudflare环境提供全球边缘部署
4. **数据安全**: 生产环境请确保数据库连接安全，使用强密码和SSL连接
