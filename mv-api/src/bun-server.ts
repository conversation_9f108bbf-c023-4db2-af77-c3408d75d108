import { Hono } from 'hono';
import { createDirectDb, DatabaseConfig } from './db';
import apiRoutes from './routes';
import 'dotenv/config';

// 定义变量类型
export type Variables = {
  db: any;
  userId?: number;
  username?: string;
  role?: string;
};

// 创建Hono应用
const app = new Hono<{ Variables: Variables }>();

// 从环境变量获取数据库配置
const dbConfig: DatabaseConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'mv_database',
  port: parseInt(process.env.DB_PORT || '3306')
};

// 创建数据库连接
let db: any;

// 初始化数据库连接
async function initDatabase() {
  try {
    db = await createDirectDb(dbConfig);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    process.exit(1);
  }
}

// 创建一个中间件来处理数据库连接
app.use('*', async (c, next) => {
  if (!db) {
    return c.json({ success: false, message: '数据库连接未初始化' }, 500);
  }
  
  c.set('db', db);
  await next();
});

// 健康检查端点
app.get('/health', (c) => {
  return c.json({ 
    success: true, 
    message: 'API服务运行正常',
    timestamp: new Date().toISOString(),
    environment: 'bun'
  });
});

// 挂载API路由
app.route('/', apiRoutes);

// 启动服务器
const PORT = parseInt(process.env.PORT || '3000');

async function startServer() {
  // 初始化数据库
  await initDatabase();
  
  console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
  console.log(`📊 数据库: ${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);
}

// 优雅关闭处理
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭服务器...');
  process.exit(0);
});

// 启动服务器
startServer().catch(console.error);

// 导出app供Bun使用
export default {
  port: PORT,
  fetch: app.fetch,
}; 