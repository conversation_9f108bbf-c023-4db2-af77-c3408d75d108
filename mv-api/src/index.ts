import { Hono } from 'hono'
import { createDb } from './db'
import apiRoutes from './routes'

// 定义Cloudflare绑定类型
export type CloudflareBindings = {
  HYPERDRIVE: any
  DB: D1Database
  JWT_SECRET?: string
  mv: KVNamespace
}

// 定义变量类型
export type Variables = {
  db: any // 使用any避免类型错误
  userId?: number
  username?: string
  role?: string
}

const app = new Hono<{ Bindings: CloudflareBindings,
  Variables: {
    db: any
  }
 }>()

// 创建一个中间件来处理数据库连接
app.use('*', async (c, next) => {
  try {
    const db = await createDb(c.env.HYPERDRIVE)
    c.set('db', db)
    await next()
  } catch (error) {
    console.error('数据库连接失败:', error)
    return c.json({ success: false, message: '数据库连接失败' }, 500)
  }
})

// 挂载API路由
app.route('/', apiRoutes)


export default app
