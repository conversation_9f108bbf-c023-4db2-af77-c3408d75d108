import { Hono } from 'hono';
import videoHandlers from './videos';

// 创建API路由
const api = new Hono();

// 挂载视频相关路由
// 公共视频API - 注意：具体路由必须放在参数路由之前
api.get('/videos', videoHandlers.getList);
api.get('/videos/getHomeData', videoHandlers.getHomeData);
api.get('/videos/search', videoHandlers.search);
api.get('/videos/recommend', videoHandlers.recommend);
api.get('/videos/:id', videoHandlers.getById);

export default api; 