import { eq, sql, like, and, not, or, desc, asc, count, isNull } from 'drizzle-orm';
import { Context } from 'hono';
import * as schema from '../db/schema';
import { CloudflareBindings, Variables } from '../index';
// 定义播放列表项类型
type PlayListItem = {
  id: number;
  lineId?: number | null;
  episode?: number | null;
  url?: string | null;
  [key: string]: any; // 允许其他属性
};

// 视频路由处理器
const videoHandlers = {
  getListByType: async (c: Context<{ Bindings: CloudflareBindings, Variables: Variables }>,typeId: number,pageSize: number=20,page: number=1,area: string='',year: number=0,director: string='',starring: string='',language: string='' ) => {
    const db = c.get('db');
    const offset = (page - 1) * pageSize;
    let condition = and(
      not(eq(schema.vodInfos.isDeleted, true)),
      eq(schema.vodInfos.typeId,typeId)
    );
    if(area){
      condition = and(condition,eq(schema.vodInfos.area,area));
    }
    if(year>0)
      condition = and(condition,eq(schema.vodInfos.year,year));
    if(director)
      condition = and(condition,eq(schema.vodInfos.director,director));
    if(starring)
      condition = and(condition,eq(schema.vodInfos.starring,starring));
    if(language)
      condition = and(condition,eq(schema.vodInfos.language,language));
    const videos = await db.select({
      id: schema.vodInfos.id,
      title: schema.vodInfos.title,
      pic: schema.vodInfos.pic,
      savePath: schema.vodPicInfo.savePath,
      year: schema.vodInfos.year,
      remark: schema.vodInfos.remark,
      area: schema.vodInfos.area
    })
    .from(schema.vodInfos)
    .leftJoin(schema.vodPicInfo, eq(schema.vodInfos.id, schema.vodPicInfo.vodInfoId))
    .where(condition)
    .orderBy(desc(schema.vodInfos.id))
    .offset(offset)
    .limit(pageSize);
    return videos.map((v: any) => ({
      id: v.id,
      title: v.title,
      pic: v.savePath ? 'https://img.jukuku.top/'+v.savePath : v.pic,
      year: v.year,
      remark: v.remark,
      area: v.area,
    }));
  },
  // 获取所有视频信息（带分页）
  async getList(c: Context<{ Bindings: CloudflareBindings, Variables: Variables }>) {
    const db = c.get('db');
    try {
      // 获取分页参数
      const page = parseInt(c.req.query('page') || '1');
      const pageSize = parseInt(c.req.query('pageSize') || '20');
      const offset = (page - 1) * pageSize;
      const typeId = parseInt(c.req.query('typeId') || '1');
      const area = c.req.query('area') || '';
      const year = parseInt(c.req.query('year') || '0');
      const director = c.req.query('director') || '';
      const starring = c.req.query('starring') || '';
      const language = c.req.query('language') || '';

      let condition = and(
        not(eq(schema.vodInfos.isDeleted, true)),
        eq(schema.vodInfos.typeId,typeId)
      );
      if(area){
        condition = and(condition,eq(schema.vodInfos.area,area));
      }
      if(year>0)
        condition = and(condition,eq(schema.vodInfos.year,year));
      if(director)
        condition = and(condition,eq(schema.vodInfos.director,director));
      if(starring)
        condition = and(condition,eq(schema.vodInfos.starring,starring));
      if(language)
        condition = and(condition,eq(schema.vodInfos.language,language));
      
      // 使用简单查询，避免复杂的关联查询
      const videos = await db.select({
        id: schema.vodInfos.id,
        title: schema.vodInfos.title,
        pic: schema.vodInfos.pic,
        savePath: schema.vodPicInfo.savePath,
        year: schema.vodInfos.year,
        remark: schema.vodInfos.remark,
        area: schema.vodInfos.area
      })
      .from(schema.vodInfos)
      .leftJoin(schema.vodPicInfo, eq(schema.vodInfos.id, schema.vodPicInfo.vodInfoId))
      .where(condition)
      .orderBy(desc(schema.vodInfos.updateTime))
      .offset(offset)
      .limit(pageSize);
      
      // 获取总记录数
      const countResult = await db.select({count:count()})
        .from(schema.vodInfos)
        .where(condition);
      
      const total = parseInt(countResult[0].count?.toString() || '0');
      const totalPages = Math.ceil(total / pageSize);
      
      return c.json({ 
        success: true, 
        data: {
          items: videos.map((v: any) => ({
            id: v.id,
            title: v.title,
            pic: v.savePath ? 'https://img.jukuku.top/'+v.savePath : v.pic,
            year: v.year,
            remark: v.remark,
            area: v.area,
          })),
          pagination: {
            page,
            pageSize,
            total,
            totalPages
          }
        }
      });
    } catch (error) {
      console.error('查询数据库失败:', error);
      return c.json({ success: false, message: '获取视频列表失败' }, 500);
    }
  },

  // 获取单个视频详情
  async getById(c: Context<{ Bindings: CloudflareBindings, Variables: Variables }>) {
    const id = parseInt(c.req.param('id'));
    if (isNaN(id)) {
      return c.json({ success: false, message: '无效的ID' }, 400);
    }

    const db = c.get('db');
    try {
      // 使用简单查询获取视频信息
      const videoResults = await db.select({
        id: schema.vodInfos.id,
        title: schema.vodInfos.title,
        pic: schema.vodInfos.pic,
        savePath: schema.vodPicInfo.savePath,
        year: schema.vodInfos.year,
        content: schema.vodInfos.content,
        director: schema.vodInfos.director,
        starring: schema.vodInfos.starring,
        area: schema.vodInfos.area,
        language: schema.vodInfos.language,
        typeId: schema.vodInfos.typeId,
        remark: schema.vodInfos.remark,
        subTitle: schema.vodInfos.subTitle,
        tags: schema.vodInfos.tags,
        backdropPath: schema.vodInfos.backdropPath,
        doubanId: schema.vodInfos.doubanId,
        imdbId: schema.vodInfos.imdbId,
        tmdbId: schema.vodInfos.tmdbId
      })
      .from(schema.vodInfos)
      .leftJoin(schema.vodPicInfo, eq(schema.vodInfos.id, schema.vodPicInfo.vodInfoId))
      .where(
        and(
          eq(schema.vodInfos.id, id),
          not(eq(schema.vodInfos.isDeleted, true))
        )
      )
      .limit(1);

      if (videoResults.length === 0) {
        return c.json({ success: false, message: '视频不存在' }, 404);
      }

      const video = videoResults[0];
      video.pic = video.savePath ? 'https://img.jukuku.top/'+video.savePath : video.pic;

      // 获取相关的在线播放链接列表
      const playList = await db.select()
        .from(schema.vodPlayList)
        .where(eq(schema.vodPlayList.vodInfoId, id))
        .orderBy(asc(schema.vodPlayList.lineId), asc(schema.vodPlayList.episode));

      // 获取相关的网盘播放链接信息
      const urls = await db.select()
        .from(schema.vodUrls)
        .where(
          and(
            eq(schema.vodUrls.vodInfoId, id),
            eq(schema.vodUrls.isInvalid, 0)
          )
        );

      // 按线路分组处理播放列表
      const playListByLine: { [key: string]: PlayListItem[] } = {};
      playList.forEach((item: PlayListItem) => {
        const lineId = item.lineId?.toString() || '0';
        if (!playListByLine[lineId]) {
          playListByLine[lineId] = [];
        }
        playListByLine[lineId].push(item);
      });

      // 组合结果
      const result = {
        ...video,
        playListByLine,
        urls
      };

      return c.json({ success: true, data: result });
    } catch (error) {
      console.error('查询数据库失败:', error);
      return c.json({ success: false, message: '获取视频详情失败' }, 500);
    }
  },

  // 按标题搜索视频
  async search(c: Context<{ Bindings: CloudflareBindings, Variables: Variables }>) {
    const query = c.req.query('q');
    if (!query) {
      return c.json({ success: false, message: '请提供搜索关键词' }, 400);
    }

    const page = parseInt(c.req.query('page') || '1');
    const pageSize = parseInt(c.req.query('pageSize') || '20');
    const offset = (page - 1) * pageSize;

    const db = c.get('db');
    try {
      // 使用前缀匹配进行搜索
      const prefixSearchCondition = and(
        not(eq(schema.vodInfos.isDeleted, true)),
        or(
          like(schema.vodInfos.title, `${query}%`),
          like(schema.vodInfos.director, `${query}%`),
          like(schema.vodInfos.starring, `${query}%`)
        )
      );

      // 获取前缀匹配的总记录数
      const prefixCountResult = await db.select({ count: count() })
        .from(schema.vodInfos)
        .where(prefixSearchCondition);
      
      const prefixTotal = parseInt(prefixCountResult[0].count?.toString() || '0');

      // 如果前缀匹配有结果，直接返回
      if (prefixTotal > 0) {
        const videos = await db.select({
          id: schema.vodInfos.id,
          title: schema.vodInfos.title,
          pic: schema.vodInfos.pic,
          savePath: schema.vodPicInfo.savePath,
          year: schema.vodInfos.year,
          remark: schema.vodInfos.remark,
          area: schema.vodInfos.area,
          typeId: schema.vodInfos.typeId
        })
        .from(schema.vodInfos)
        .leftJoin(schema.vodPicInfo, eq(schema.vodInfos.id, schema.vodPicInfo.vodInfoId))
        .where(prefixSearchCondition)
        .orderBy(desc(schema.vodInfos.updateTime))
        .offset(offset)
        .limit(pageSize);

        const totalPages = Math.ceil(prefixTotal / pageSize);

        return c.json({ 
          success: true, 
          data: {
            items: videos.map((v: any) => ({
              id: v.id,
              title: v.title,
              pic: v.savePath ? 'https://img.jukuku.top/'+v.savePath : v.pic,
              year: v.year,
              remark: v.remark,
              area: v.area,
            })),
            pagination: {
              page,
              pageSize,
              total: prefixTotal,
              totalPages
            },
            searchType: 'prefix'
          }
        });
      }

      // 如果前缀匹配没结果，尝试包含匹配
      const containsSearchCondition = and(
        not(eq(schema.vodInfos.isDeleted, true)),
        or(
          like(schema.vodInfos.title, `%${query}%`),
          like(schema.vodInfos.director, `%${query}%`),
          like(schema.vodInfos.starring, `%${query}%`)
        )
      );

      const containsCountResult = await db.select({ count: count() })
        .from(schema.vodInfos)
        .where(containsSearchCondition);
      
      const containsTotal = parseInt(containsCountResult[0].count?.toString() || '0');

      // 如果包含匹配也没有结果，返回空数组
      if (containsTotal === 0) {
        return c.json({ 
          success: true, 
          data: {
            items: [],
            pagination: {
              page,
              pageSize,
              total: 0,
              totalPages: 0
            },
            searchType: 'none'
          }
        });
      }

      // 返回包含匹配的结果
      const videos = await db.select({
        id: schema.vodInfos.id,
        title: schema.vodInfos.title,
        pic: schema.vodInfos.pic,
        savePath: schema.vodPicInfo.savePath,
        year: schema.vodInfos.year,
        remark: schema.vodInfos.remark,
        area: schema.vodInfos.area,
        typeId: schema.vodInfos.typeId
      })
      .from(schema.vodInfos)
      .leftJoin(schema.vodPicInfo, eq(schema.vodInfos.id, schema.vodPicInfo.vodInfoId))
      .where(containsSearchCondition)
      .orderBy(desc(schema.vodInfos.updateTime))
      .offset(offset)
      .limit(pageSize);

      const totalPages = Math.ceil(containsTotal / pageSize);

      return c.json({ 
        success: true, 
        data: {
          items: videos.map((v: any) => ({
            id: v.id,
            title: v.title,
            pic: v.savePath ? 'https://img.jukuku.top/'+v.savePath : v.pic,
            year: v.year,
            remark: v.remark,
            area: v.area,
          })),
          pagination: {
            page,
            pageSize,
            total: containsTotal,
            totalPages
          },
          searchType: 'contains'
        }
      });
    } catch (error) {
      console.error('搜索失败:', error);
      return c.json({ success: false, message: '搜索视频失败' }, 500);
    }
  },
  async recommend(c: Context<{ Bindings: CloudflareBindings, Variables: Variables }>) {
    const db = c.get('db');
    const query = db.select({
      id: schema.vodInfos.id,
      title: schema.vodInfos.title,
      pic: schema.vodInfos.pic,
      savePath: schema.vodPicInfo.savePath,
      backdropPath: schema.vodInfos.backdropPath,
      typeId: schema.vodInfos.typeId,
      rating: schema.recentHot.rating,
      year: schema.vodInfos.year,
      area: schema.vodInfos.area,
      director: schema.vodInfos.director,
      starring: schema.vodInfos.starring,
      language: schema.vodInfos.language,
      remark: schema.vodInfos.remark,
      subtitle: schema.recentHot.subtitle,
    })
    .from(schema.recentHot)
    .innerJoin(schema.vodInfos, eq(schema.recentHot.vodInfoId, schema.vodInfos.id))
    .leftJoin(schema.vodPicInfo, eq(schema.vodInfos.id, schema.vodPicInfo.vodInfoId));
    
    // 添加where条件：不删除、有背景图、类型为电影(1)或电视剧(2)
    const whereQuery = query.where(
      and(
        not(eq(schema.vodInfos.isDeleted, true)),
        not(isNull(schema.vodInfos.backdropPath)),
        or(
          eq(schema.vodInfos.typeId, 1),  // 电影
          eq(schema.vodInfos.typeId, 2)   // 电视剧
        )
      )
    );
    
    // 添加排序和限制
    const videos = await whereQuery
      .orderBy(schema.recentHot.id);
    
    const result = videos.map((v: any) => ({
      id: v.id,
      title: v.title,
      pic: v.savePath ? 'https://img.jukuku.top/'+v.savePath : v.pic,
      backdropPath: v.backdropPath,
      typeId: v.typeId,
      rating: v.rating,
      year: v.year,
      area: v.area,
      director: v.director,
      starring: v.starring,
      language: v.language,
      remark: v.remark,
      subTitle: v.subtitle,
    }));
    return c.json({ success: true, data: result });
  },

  // 获取轮播图数据
  async getBannerVideos(c: Context<{ Bindings: CloudflareBindings, Variables: Variables }>) {
    const db = c.get('db');
      const query = db.select({
        id: schema.vodInfos.id,
        title: schema.vodInfos.title,
        pic: schema.vodInfos.pic,
        savePath: schema.vodPicInfo.savePath,
        backdropPath: schema.vodInfos.backdropPath,
        typeId: schema.vodInfos.typeId,
        rating: schema.recentHot.rating,
        year: schema.vodInfos.year,
        area: schema.vodInfos.area,
        director: schema.vodInfos.director,
        starring: schema.vodInfos.starring,
        language: schema.vodInfos.language,
        remark: schema.vodInfos.remark,
        subtitle: schema.recentHot.subtitle,
      })
      .from(schema.recentHot)
      .innerJoin(schema.vodInfos, eq(schema.recentHot.vodInfoId, schema.vodInfos.id))
      .leftJoin(schema.vodPicInfo, eq(schema.vodInfos.id, schema.vodPicInfo.vodInfoId));
      
      // 添加where条件：不删除、有背景图、类型为电影(1)或电视剧(2)
      const whereQuery = query.where(
        and(
          not(eq(schema.vodInfos.isDeleted, true)),
          not(isNull(schema.vodInfos.backdropPath)),
          or(
            eq(schema.vodInfos.typeId, 1),  // 电影
            eq(schema.vodInfos.typeId, 2)   // 电视剧
          )
        )
      );
      
      // 添加排序和限制
      const videos = await whereQuery
        .orderBy(schema.recentHot.id);
      
      return videos.map((v: any) => ({
        id: v.id,
        title: v.title,
        pic: v.savePath ? 'https://img.jukuku.top/'+v.savePath : v.pic,
        backdropPath: v.backdropPath,
        typeId: v.typeId,
        rating: v.rating,
        year: v.year,
        area: v.area,
        director: v.director,
        starring: v.starring,
        language: v.language,
        remark: v.remark,
        subTitle: v.subtitle,
      }));
  },
  async getHomeData(c: Context<{ Bindings: CloudflareBindings, Variables: Variables }>) {
    const bannerVideos = await videoHandlers.getBannerVideos(c);
    const movieVideos = await videoHandlers.getListByType(c,1);
    const tvVideos = await videoHandlers.getListByType(c,2);
    const animeVideos = await videoHandlers.getListByType(c,3);
    const varietyVideos = await videoHandlers.getListByType(c,4);
    const documentaryVideos = await videoHandlers.getListByType(c,5);
    const shortVideos = await videoHandlers.getListByType(c,7);
    
    return c.json({ success: true, data: {bannerVideos, movieVideos, tvVideos, animeVideos, varietyVideos, documentaryVideos, shortVideos } });
  }
};

export default videoHandlers; 