import { drizzle } from 'drizzle-orm/mysql2';
import { createConnection, Connection } from 'mysql2/promise';
import * as schema from './schema';

// 数据库连接配置类型
export interface DatabaseConfig {
  host: string;
  user: string;
  password: string;
  database: string;
  port: number;
}

/**
 * 创建Drizzle数据库连接 - 用于Cloudflare Workers环境
 * @param hyperdriveBinding Cloudflare HYPERDRIVE绑定
 * @returns Drizzle ORM实例
 */
export async function createDb(hyperdriveBinding: any): Promise<any> {
  // 使用HYPERDRIVE绑定的凭据创建连接
  const connection = await createConnection({
    host: hyperdriveBinding.host,
    user: hyperdriveBinding.user,
    password: hyperdriveBinding.password,
    database: hyperdriveBinding.database,
    port: hyperdriveBinding.port,
    // 必须设置，以启用mysql2与Workers的兼容性
    disableEval: true
  });

  // 使用mysql2驱动创建Drizzle客户端
  return drizzle(connection, { schema, mode: 'default' });
}

/**
 * 创建Drizzle数据库连接 - 用于Bun环境直连MySQL
 * @param config 数据库配置
 * @returns Drizzle ORM实例
 */
export async function createDirectDb(config: DatabaseConfig): Promise<any> {
  // 直接使用MySQL连接配置创建连接
  const connection = await createConnection({
    host: config.host,
    user: config.user,
    password: config.password,
    database: config.database,
    port: config.port
  });

  // 使用mysql2驱动创建Drizzle客户端
  return drizzle(connection, { schema, mode: 'default' });
}

export type Database = any; 