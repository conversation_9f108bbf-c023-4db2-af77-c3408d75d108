import { mysqlTable, int, varchar, text, tinyint, bigint, char, datetime, boolean, index, float } from "drizzle-orm/mysql-core";
import { relations } from "drizzle-orm";

// 云存储文件信息表
export const cloudFiles = mysqlTable("cloud_files", {
  id: int("id").primaryKey().autoincrement(),
  cloudId: int("cloud_id"),
  vodUrlId: int("vod_url_id"),
  fid: char("fid", { length: 50 }),
  pdirFid: char("pdir_fid", { length: 50 }),
  fileName: varchar("file_name", { length: 255 }),
  size: bigint("size", { mode: "bigint" }),
  fileType: int("file_type"),
  formatType: char("format_type", { length: 32 }),
  duration: int("duration"),
  fps: int("fps"),
  videoHeight: int("video_height"),
  videoWidth: int("video_width"),
  videoMaxResolution: char("video_max_resolution", { length: 10 }),
  lCreatedAt: bigint("l_created_at", { mode: "bigint" }),
  lUpdatedAt: bigint("l_updated_at", { mode: "bigint" }),
});

// 视频下载列表
export const vodDownList = mysqlTable("vod_down_list", {
  id: int("id").primaryKey().autoincrement(),
  vodInfoId: int("vod_info_id"),
  siteId: tinyint("site_id"),
  siteVodId: varchar("site_vod_id", { length: 10 }),
  createTime: datetime("create_time"),
  url: varchar("url", { length: 40 }),
}, (table) => {
  return {
    urlIdx: index("ix_vod_down_list_url").on(table.url),
    vodInfoIdIdx: index("vod_info_id").on(table.vodInfoId),
  };
});

// 视频信息表
export const vodInfos = mysqlTable("vod_infos", {
  id: int("id").primaryKey().autoincrement(),
  title: varchar("title", { length: 50 }),
  director: varchar("director", { length: 128 }),
  year: int("year"),
  content: text("content"),
  pic: varchar("pic", { length: 256 }),
  starring: varchar("starring", { length: 512 }),
  typeId: int("type_id"),
  remark: varchar("remark", { length: 80 }),
  subTitle: varchar("sub_title", { length: 80 }),
  tags: varchar("tags", { length: 50 }),
  tags2: varchar("tags2", { length: 30 }),
  createTime: datetime("create_time"),
  updateTime: datetime("update_time"),
  area: varchar("area", { length: 50 }),
  language: varchar("language", { length: 50 }),
  bianju: varchar("bianju", { length: 100 }),
  stime: varchar("stime", { length: 100 }),
  times: varchar("times", { length: 100 }),
  ename: varchar("ename", { length: 256 }),
  doubanId: varchar("douban_id", { length: 8 }),
  imdbId: varchar("imdb_id", { length: 10 }),
  roId: varchar("ro_id", { length: 100 }),
  tmdbId: int("tmdb_id"),
  backdropPath: char("backdrop_path", { length: 50 }),
  isDeleted: boolean("is_deleted").default(false),
}, (table) => {
  return {
    titleYearIdx: index("ix_title_year").on(table.title, table.year),
  };
});

// 视频播放列表
export const vodPlayList = mysqlTable("vod_play_list", {
  id: int("id").primaryKey().autoincrement(),
  vodInfoId: int("vod_info_id"),
  siteId: tinyint("site_id"),
  siteVodId: char("site_vod_id", { length: 4 }),
  lineId: tinyint("line_id"),
  episode: int("episode"),
  url: varchar("url", { length: 256 }),
  createTime: datetime("create_time"),
}, (table) => {
  return {
    siteVodIdLineIdIdx: index("ix_site_vod_id").on(table.siteVodId, table.lineId),
    urlIdx: index("ix_vod_play_list_url").on(table.url),
    vodInfoIdIdx: index("vod_info_id").on(table.vodInfoId),
  };
});

// 视频站点ID关联表
export const vodSiteIds = mysqlTable('vod_site_ids', {
  id: int('id').primaryKey().autoincrement(),
  vodInfoId: int('vod_info_id').notNull(),
  siteId: int('site_id').notNull(),
  siteVodId: varchar('site_vod_id', { length: 255 }),
  typeId: int('type_id'),
  createTime: datetime('create_time'),
}, (table) => ({
  siteIdVodIdIdx: index('ix_site_id').on(table.siteId, table.siteVodId),
  siteVodIdSiteIdIdx: index('ix_site_vod_id').on(table.siteVodId, table.siteId),
  vodInfoIdIdx: index('vod_site_ids_FK_0_0').on(table.vodInfoId),
}));

// 视频URL信息表
export const vodUrls = mysqlTable("vod_urls", {
  id: int("id").primaryKey().autoincrement(),
  cloudId: int("cloudId"),
  url: varchar("url", { length: 100 }),
  createTime: datetime("create_time"),
  vodInfoId: int("vod_info_id"),
  checkStatus: int("check_status").default(0),
  isInvalid: int("is_invalid").default(0),
  siteId: int("site_id"),
  pwd: char("pwd", { length: 6 }),
  stoken: char("stoken", { length: 50 }),
  invalidReason: varchar("invalid_reason", { length: 100 }),
}, (table) => {
  return {
    urlIdx: index("ix_url").on(table.url),
    vodInfoIdIdx: index("vod_urls_FK_0_0").on(table.vodInfoId),
  };
});

// 视频图片信息表
export const vodPicInfo = mysqlTable("vod_pic_info", {
  vodInfoId: int("vod_info_id").primaryKey(),
  picUrl: varchar("pic_url", { length: 255 }),
  savePath: varchar("save_path", { length: 255 }),
  isDownloaded: boolean("is_downloaded").default(false),
  createTime: datetime("create_time"),
  updateTime: datetime("update_time"),
});
// 最近热门
export const recentHot = mysqlTable("recent_hot", {
  id: int("id").primaryKey().autoincrement(),
  vodInfoId: int("vod_info_id"),
  title: varchar("title", { length: 255 }),
  typeId: tinyint("type_id"),
  doubanId: int("douban_id"),
  episodesInfo: varchar("episodes_info", { length: 128 }),
  rating: float("rating"),
  subtitle: varchar("subtitle", { length: 255 }),
  createTime: datetime("create_time"),
});

// 定义关系
export const cloudFilesRelations = relations(cloudFiles, ({ one }) => ({
  vodUrl: one(vodUrls, {
    fields: [cloudFiles.vodUrlId],
    references: [vodUrls.id],
  }),
}));

export const vodDownListRelations = relations(vodDownList, ({ one }) => ({
  vodInfo: one(vodInfos, {
    fields: [vodDownList.vodInfoId],
    references: [vodInfos.id],
  }),
}));

export const vodInfosRelations = relations(vodInfos, ({ many }) => ({
  vodDownList: many(vodDownList),
  vodPlayList: many(vodPlayList),
  vodSiteIds: many(vodSiteIds),
  vodUrls: many(vodUrls),
  vodPicInfo: many(vodPicInfo),
}));

export const vodPlayListRelations = relations(vodPlayList, ({ one }) => ({
  vodInfo: one(vodInfos, {
    fields: [vodPlayList.vodInfoId],
    references: [vodInfos.id],
  }),
}));

export const vodSiteIdsRelations = relations(vodSiteIds, ({ one }) => ({
  vodInfo: one(vodInfos, {
    fields: [vodSiteIds.vodInfoId],
    references: [vodInfos.id],
  }),
}));

export const vodUrlsRelations = relations(vodUrls, ({ one, many }) => ({
  vodInfo: one(vodInfos, {
    fields: [vodUrls.vodInfoId],
    references: [vodInfos.id],
  }),
  cloudFiles: many(cloudFiles),
})); 
export const vodPicInfoRelations = relations(vodPicInfo, ({ one }) => ({
  vodInfo: one(vodInfos, {
    fields: [vodPicInfo.vodInfoId],
    references: [vodInfos.id],
  }),
}));