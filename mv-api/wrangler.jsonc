{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "mv-api",
  "main": "src/index.ts",
  "compatibility_date": "2025-05-25",
  "observability": {
    "enabled": true
	},
  "compatibility_flags": [
    "nodejs_compat"
  ],
	"hyperdrive": [
      {
        "binding": "HYPERDRIVE",
        "id": "3ec790b68c1c4fe2af2ac146cd95ab45",
        "localConnectionString": "mysql://vod_db:8XnJfRbbSF4Xaah5@49.235.64.166:3306/vod_db"
      }
    ],
    "d1_databases": [
      {
      "binding": "DB",
      "database_name": "movie-site-db",
      "database_id": "efc29aa7-accd-4f86-98a4-de0cd46b12fb"
      }
    ],
    "kv_namespaces": [
      {
        "binding": "mv",
        "id": "8845f6eb33b04418bb78db1d117554d7"
      }
    ],
  // "vars": {
  //   "MY_VAR": "my-variable"
  // },
  // "kv_namespaces": [
  //   {
  //     "binding": "MY_KV_NAMESPACE",
  //     "id": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  //   }
  // ],
  // "r2_buckets": [
  //   {
  //     "binding": "MY_BUCKET",
  //     "bucket_name": "my-bucket"
  //   }
  // ],
  // "d1_databases": [
  //   {
  //     "binding": "MY_DB",
  //     "database_name": "my-database",
  //     "database_id": ""
  //   }
  // ],
  // "ai": {
  //   "binding": "AI"
  // },
  // "observability": {
  //   "enabled": true,
  //   "head_sampling_rate": 1
  // }
}
