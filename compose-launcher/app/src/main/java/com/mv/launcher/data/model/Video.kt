package com.mv.launcher.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class Video(
    val id: Int,
    val title: String?,
    val pic: String?,
    val year: Int?,
    val content: String?,
    val director: String?,
    val starring: String?,
    val area: String?,
    val language: String?,
    val typeId: Int?,
    val remark: String?,
    val subTitle: String?,
    val tags: String?,
    val createTime: String?,
    val updateTime: String?,
    val doubanId: String?,
    val imdbId: String?,
    val tmdbId: Int?,
    val backdropPath: String?,
    val rating: Double?,
    val index: Int?,
    
    // 详情页额外字段
    val playListByLine: Map<String, List<PlayListItem>>? = null,
    val urls: List<VodUrl>? = null,
) : Parcelable

@Parcelize
data class PlayListItem(
    val id: Int,
    val vodInfoId: Int?,
    val lineId: Int?,
    val episode: Int?,
    val url: String?
) : Parcelable

@Parcelize
data class VodUrl(
    val id: Int,
    val cloudId: Int?,
    val url: String?,
    val vodInfoId: Int?,
    val checkStatus: Int?,
    val isInvalid: Int?,
    val pwd: String?,
    val stoken: String?
) : Parcelable

@Parcelize
data class BannerItem(
    val id: Int,
    val title: String,
    val pic: String?,
    val backdropPath: String?,
    val typeId: Int?,
    val rating: Double?,
    val year: Int?,
    val area: String?,
    val director: String?,
    val starring: String?,
    val language: String?,
    val remark: String?
) : Parcelable

// API响应包装类
data class VideoListResponse(
    val success: Boolean,
    val data: VideoListData?,
    val message: String?
)

data class VideoListData(
    val items: List<Video>,
    val pagination: Pagination
)

data class VideoDetailResponse(
    val success: Boolean,
    val data: Video?,
    val message: String?
)

data class HomeDataResponse(
    val success: Boolean,
    val data: HomeData?,
    val message: String?
)

data class HomeData(
    val bannerVideos: List<BannerItem>?,
    val movieVideos: List<Video>?,
    val tvVideos: List<Video>?,
    val animeVideos: List<Video>?,
    val varietyVideos: List<Video>?,
    val documentaryVideos: List<Video>?,
    val shortVideos: List<Video>?
)

data class RecommendVideosResponse(
    val success: Boolean,
    val data: List<Video>?,
    val message: String?
)

data class Pagination(
    val page: Int,
    val pageSize: Int,
    val total: Int,
    val totalPages: Int
)

// 分类模型
data class VideoCategory(
    val id: Int,
    val name: String
)

// 扩展函数：将Video转换为MoviceData
fun Video.toMoviceData(): com.mv.launcher.bean.MoviceData {
    return com.mv.launcher.bean.MoviceData(
        url = getImageUrl(this.pic),
        title = this.title ?: "",
        bgUrl = "https://image.tmdb.org/t/p/original${this.backdropPath}",
        detail = this.subTitle ?: "",
        index = this.index ?: 0,
        rating = this.rating ?: 0.0
    )
}

// 扩展函数：将BannerItem转换为MoviceData
fun BannerItem.toMoviceData(): com.mv.launcher.bean.MoviceData {
    return com.mv.launcher.bean.MoviceData(
        url = getImageUrl(this.pic),
        title = this.title,
        bgUrl = "https://image.tmdb.org/t/p/original${this.backdropPath}",
        detail = "${this.year ?: ""} ${this.area ?: ""} ${this.director ?: ""}"
    )
}

// 图片URL处理函数
private fun getImageUrl(pic: String?): String {
    return when {
        pic.isNullOrBlank() -> "https://img.jukuku.top/default.jpg"
        pic.startsWith("http://") || pic.startsWith("https://") -> pic
        else -> "https://img.jukuku.top/$pic"
    }
}
