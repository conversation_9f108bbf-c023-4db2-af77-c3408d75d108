package com.mv.launcher.di

import android.content.Context
import androidx.room.Room
import com.mv.launcher.data.database.LauncherDatabase
import com.mv.launcher.data.database.dao.CachedVideoDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): LauncherDatabase {
        return Room.databaseBuilder(
            context,
            LauncherDatabase::class.java,
            LauncherDatabase.DATABASE_NAME
        ).build()
    }
    
    @Provides
    fun provideCachedVideoDao(database: LauncherDatabase): CachedVideoDao {
        return database.cachedVideoDao()
    }
}
