package com.mv.launcher.data.database.dao

import androidx.room.*
import com.mv.launcher.data.database.entity.CachedVideoEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface CachedVideoDao {
    
    /**
     * 根据缓存类型获取视频列表
     */
    @Query("SELECT * FROM cached_videos WHERE cacheType = :cacheType ORDER BY cacheTime DESC")
    suspend fun getVideosByType(cacheType: String): List<CachedVideoEntity>
    
    /**
     * 根据缓存类型获取视频列表（Flow）
     */
    @Query("SELECT * FROM cached_videos WHERE cacheType = :cacheType ORDER BY cacheTime DESC")
    fun getVideosByTypeFlow(cacheType: String): Flow<List<CachedVideoEntity>>
    
    /**
     * 根据ID获取视频
     */
    @Query("SELECT * FROM cached_videos WHERE id = :videoId")
    suspend fun getVideoById(videoId: Int): CachedVideoEntity?
    
    /**
     * 插入单个视频
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertVideo(video: CachedVideoEntity)
    
    /**
     * 插入视频列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertVideos(videos: List<CachedVideoEntity>)
    
    /**
     * 更新视频
     */
    @Update
    suspend fun updateVideo(video: CachedVideoEntity)
    
    /**
     * 删除视频
     */
    @Delete
    suspend fun deleteVideo(video: CachedVideoEntity)
    
    /**
     * 根据缓存类型删除视频
     */
    @Query("DELETE FROM cached_videos WHERE cacheType = :cacheType")
    suspend fun deleteVideosByType(cacheType: String)
    
    /**
     * 删除所有缓存视频
     */
    @Query("DELETE FROM cached_videos")
    suspend fun deleteAllVideos()
    
    /**
     * 删除过期的缓存数据
     * @param expireTime 过期时间戳
     */
    @Query("DELETE FROM cached_videos WHERE cacheTime < :expireTime")
    suspend fun deleteExpiredVideos(expireTime: Long)
    
    /**
     * 获取指定类型的缓存数量
     */
    @Query("SELECT COUNT(*) FROM cached_videos WHERE cacheType = :cacheType")
    suspend fun getVideoCountByType(cacheType: String): Int
    
    /**
     * 获取最新的缓存时间
     */
    @Query("SELECT MAX(cacheTime) FROM cached_videos WHERE cacheType = :cacheType")
    suspend fun getLatestCacheTime(cacheType: String): Long?
    
    /**
     * 检查缓存是否存在且未过期
     * @param cacheType 缓存类型
     * @param expireTime 过期时间戳
     */
    @Query("SELECT COUNT(*) > 0 FROM cached_videos WHERE cacheType = :cacheType AND cacheTime > :expireTime")
    suspend fun isCacheValid(cacheType: String, expireTime: Long): Boolean
}
