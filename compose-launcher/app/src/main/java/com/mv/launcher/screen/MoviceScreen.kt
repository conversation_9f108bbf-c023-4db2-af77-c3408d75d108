@file:OptIn(ExperimentalFoundationApi::class, ExperimentalTvMaterial3Api::class,
    ExperimentalComposeUiApi::class, ExperimentalTvMaterial3Api::class
)

package com.mv.launcher.screen

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.Crossfade
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.focusGroup
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.focus.focusProperties
import androidx.compose.ui.focus.focusRestorer
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInWindow
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.tv.foundation.PivotOffsets
import androidx.tv.foundation.lazy.list.TvLazyColumn
import androidx.tv.foundation.lazy.list.TvLazyRow
import androidx.tv.foundation.lazy.list.rememberTvLazyListState
import androidx.tv.material3.Border
import androidx.tv.material3.CardDefaults
import androidx.tv.material3.CardLayoutDefaults
import androidx.tv.material3.ExperimentalTvMaterial3Api
import androidx.tv.material3.FilterChip
import androidx.tv.material3.FilterChipDefaults
import androidx.tv.material3.ImmersiveList
import androidx.tv.material3.MaterialTheme
import androidx.tv.material3.ShapeDefaults
import androidx.tv.material3.StandardCardLayout
import androidx.tv.material3.Text
import androidx.tv.material3.Button
import androidx.tv.material3.ButtonDefaults
import coil.compose.AsyncImage
import com.mv.launcher.R
import com.mv.launcher.bean.MoviceData
import com.mv.launcher.viewmodel.MovieViewModel
import com.mv.launcher.viewmodel.MovieUiState
import com.mv.launcher.utils.KLog

@Composable
fun MoviceScreen(
    viewModel: MovieViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val tvLazyListState = rememberTvLazyListState()

    when {
        uiState.isLoading -> {
            LoadingContent()
        }
        uiState.error != null -> {
            ErrorContent(
                error = uiState.error!!,
                onRetry = { viewModel.retryLoadData() }
            )
        }
        else -> {
            SuccessContent(
                uiState = uiState,
                tvLazyListState = tvLazyListState
            )
        }
    }
}

@Composable
fun MovicesImmersList(datas:List<MoviceData>) {
    var isListFocused by remember { mutableStateOf(false) }
    var currentItemIndex by remember { mutableStateOf(0) }
    var currentYCoord: Float? by remember { mutableStateOf(null) }
    var isAutoSwitching by remember { mutableStateOf(true) }
    var isAutoFocusChanging by remember { mutableStateOf(false) } // 标记是否为自动切换导致的焦点变化
    val coroutineScope = rememberCoroutineScope()

    // 为每个视频卡片创建FocusRequester
    val focusRequesters = remember(datas.size) {
        KLog.d("创建FocusRequester列表，大小: ${datas.size}")
        List(datas.size) { index ->
            KLog.d("创建FocusRequester[$index]")
            FocusRequester()
        }
    }

    // 监控isAutoSwitching状态变化
    LaunchedEffect(isAutoSwitching) {
        KLog.d("isAutoSwitching状态变化: $isAutoSwitching, isAutoFocusChanging: $isAutoFocusChanging")
    }

    // 定时自动切换逻辑
    LaunchedEffect(datas) {
        if (datas.isNotEmpty()) {
            while (true) {
                delay(5000) // 每5秒切换一次
                if (isAutoSwitching && datas.isNotEmpty()) {
                    val nextIndex = (currentItemIndex + 1) % datas.size
                    KLog.d("自动切换到下一个项目: $nextIndex (当前: $currentItemIndex, 总数: ${datas.size})")

                    // 提前设置自动焦点变化标志，在任何状态变化之前
                    isAutoFocusChanging = true

                    // 先延迟一小段时间确保标志设置生效
                    delay(50)

                    currentItemIndex = nextIndex

                    // 自动切换时请求焦点到对应的卡片
                    try {
                        if (nextIndex < focusRequesters.size) {
                            focusRequesters[nextIndex].requestFocus()
                            KLog.d("自动切换完成，当前项目: $nextIndex，已请求焦点")
                        } else {
                            KLog.e("焦点请求器索引超出范围: $nextIndex >= ${focusRequesters.size}")
                        }
                    } catch (e: Exception) {
                        KLog.e("请求焦点失败: ${e.message}")
                    }

                    // 延迟重置标志，给焦点变化事件足够的时间
                    delay(1000) // 增加到1秒，确保所有焦点事件都处理完成
                    isAutoFocusChanging = false
                    KLog.d("重置自动焦点变化标志")
                }
            }
        }
    }

    // 监控用户焦点状态变化，但不自动暂停切换
    LaunchedEffect(isListFocused) {
        KLog.d("焦点状态变化: isListFocused=$isListFocused, isAutoFocusChanging=$isAutoFocusChanging")
        // 移除自动暂停逻辑，改为仅在用户手动操作时暂停
    }

    ImmersiveList(
        modifier = Modifier.onGloballyPositioned { currentYCoord = it.positionInWindow().y },
        background = { index, listHasFocus ->
            if (isListFocused != listHasFocus) {
                KLog.d("ImmersiveList焦点状态变化: $isListFocused -> $listHasFocus, isAutoFocusChanging: $isAutoFocusChanging")
            }
            isListFocused = listHasFocus
            val height = 750.dp
            AnimatedVisibility(
//                visible = isListFocused,
                visible = true,
                enter = fadeIn() + expandVertically(),
                exit = fadeOut() + shrinkVertically(),
                modifier = Modifier
                    .height(height)
                    .gradientOverlay(colorResource(R.color.surface))
            ) {
                val movie = remember(datas, currentItemIndex) {
                    datas[currentItemIndex]
                }
                Crossfade(targetState = movie.bgUrl, label = "CrossfadeUriTest") { posterUri ->
                    AsyncImage(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(height),
                        model = posterUri,
                        contentDescription = null,
                        contentScale = ContentScale.Crop
                    )
                }
            }
        },
        list = {
            Column(
                modifier = Modifier.focusGroup()
            ) {
//                    if (isListFocused) {
                AnimatedVisibility(
                    visible = isListFocused || isAutoSwitching, // 自动切换时也显示信息
                    enter = slideInHorizontally(initialOffsetX ={it}) + fadeIn(),
                    exit = slideOutHorizontally(targetOffsetX = {it}) + fadeOut()
                ) {
                    val movie = remember(datas, currentItemIndex) {
                        datas[currentItemIndex]
                    }
                    Column(
                        modifier = Modifier.padding(start = 90.dp, bottom = 40.dp)
                    ) {
                        Text(
                            text = movie.title,
                            style = MaterialTheme.typography.displaySmall.copy(fontSize = 40.sp),
                            color = Color.White,
                            modifier = Modifier.basicMarquee() // 添加跑马灯效果
                        )
                        Spacer(modifier = Modifier.padding(top = 8.dp))
                        Text(
                            modifier = Modifier.fillMaxWidth(0.5f),
                            text = movie.detail,
                            style = MaterialTheme.typography.bodyLarge.copy(fontSize = 23.sp),
                            color = Color.White.copy(alpha = 0.75f),
                            fontWeight = FontWeight.Light,
                            maxLines = 3, // 限制行数
                            overflow = TextOverflow.Ellipsis
                        )

                        // 添加自动切换状态指示
                        if (isAutoSwitching && !isListFocused) {
                            Spacer(modifier = Modifier.padding(top = 8.dp))
                            // Text(
                            //     text = "自动播放中...",
                            //     style = MaterialTheme.typography.bodySmall,
                            //     color = Color.White.copy(alpha = 0.6f)
                            // )
                        }
                    }
                }
                //
                TvLazyRow(
                    modifier = Modifier
                        .focusRestorer()
                        .height(200.dp),
                    contentPadding = PaddingValues(16.dp),
                    horizontalArrangement = Arrangement.spacedBy(20.dp)
                ) {
                    item {
                        Spacer(modifier = Modifier.width(50.dp))
                    }
                    datas.forEachIndexed { index, moviceData ->
                        item {
                            MoviesRowItem(
                                index = index,
                                width = 270.dp,
                                height = 180.dp,
                                data = moviceData,
                                focusRequester = focusRequesters.getOrNull(index),
                                focusedItemIndex = {
                                    currentItemIndex = it
                                    // 只有在非自动切换时才暂停自动切换
                                    if (!isAutoFocusChanging) {
                                        KLog.d("用户手动选择卡片: $it，暂停自动切换")
                                        isAutoSwitching = false

                                        // 启动一个协程，在用户停止操作后恢复自动切换
                                        coroutineScope.launch {
                                            delay(5000) // 5秒后恢复自动切换
                                            if (!isAutoSwitching && !isAutoFocusChanging) {
                                                KLog.d("用户操作超时，恢复自动切换")
                                                isAutoSwitching = true
                                            }
                                        }
                                    } else {
                                        KLog.d("自动切换导致的焦点变化: $it，继续自动切换")
                                    }
                                }
                            )
                        }
                    }
                }
            }
        })
}

@Composable
fun MoviesLazyRow(
    title:String="测试",
    datas:List<MoviceData>,
    isNoTitle:Boolean=false,
    contentPadding:Dp=30.dp,
    itemWidth:Dp=223.dp,
    itemHeight:Dp=310.dp
) {
    Column(
        modifier = Modifier.focusGroup(),
    ) {
        if (!isNoTitle) {
            Text(
                text = title,
                style = MaterialTheme.typography.headlineLarge.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 36.sp
                ),
                modifier = Modifier
                    .alpha(1f)
                    .padding(start = 90.dp)
                    .padding(vertical = 20.dp),
                color = Color.White
            )
        }
        Box(
            modifier = Modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center
        ) {
            TvLazyRow(
                pivotOffsets = PivotOffsets(parentFraction = 0.42f),
                contentPadding = PaddingValues(contentPadding),
                horizontalArrangement = Arrangement.spacedBy(30.dp)
            ) {
                items(datas.size) {
                    MoviesRowItem(it, data=datas[it], width=itemWidth, height = itemHeight)
                }
            }
        }
    }
}

@Composable
fun MoviesRowItem(
    index: Int = 0,
    width: Dp = 223.dp,
    height: Dp = 310.dp,
    data: MoviceData,
    focusRequester: FocusRequester? = null,
    focusedItemIndex: (index: Int) -> Unit = {}
) {
    var isItemFocused by remember { mutableStateOf(false) }

    StandardCardLayout(
        modifier = Modifier
            .width(width)
            .then(
                if (focusRequester != null) {
                    Modifier.focusRequester(focusRequester)
                } else {
                    Modifier
                }
            )
            .onFocusChanged {
                isItemFocused = it.isFocused
                if (isItemFocused) {
                    focusedItemIndex(index)
                    KLog.d("卡片 $index 获得焦点: ${data.title}")
                }
            }
            .focusProperties {

            },
        imageCard = {
            CardLayoutDefaults.ImageCard(
                onClick = {},
                shape = CardDefaults.shape(ShapeDefaults.ExtraSmall),
                border = CardDefaults.border(
                    focusedBorder = Border(
                        border = BorderStroke(
                            width = 3.dp,
                            color = Color.White
                        ),
                        shape = ShapeDefaults.ExtraSmall
                    )
                ),
                scale = CardDefaults.scale(focusedScale = 1.0f),
                interactionSource = it
            ) {
                AsyncImage(
                    modifier= Modifier
                        .fillMaxWidth()
                        .height(height),
                    model = data.url,
                    contentScale = ContentScale.Crop,
                    contentDescription ="${data.title}",
                    error = painterResource(id = R.drawable.movice_pic1)
                )
            }
        },
        title = {
            if (data.title.isNotEmpty()) {
                val movieNameAlpha by animateFloatAsState(
                    targetValue = if (isItemFocused) 1f else 0f,
                    label = "",
                )
                Text(
                    text = data.title,
                    color = Color.White,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.SemiBold, fontSize = 20.sp
                    ),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .alpha(movieNameAlpha)
                        .fillMaxWidth()
                        .padding(top = 4.dp)
                        .basicMarquee()/*跑马灯*/,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        })
}

val columnDatas = listOf("票房榜", "喜剧", "战争", "动画", "科幻", "热血动作", "全部")


@Composable
fun MovicesColumnItem() {
    TvLazyRow(modifier = Modifier.padding(start = 90.dp, bottom = 30.dp)) {
        columnDatas.forEachIndexed { index,text->
            item {
                FilterChip(
                    modifier = Modifier
                        .padding(end = 20.dp)
                        .width(232.dp)
                        .height(100.dp),
                    shape = FilterChipDefaults.shape(shape = ShapeDefaults.ExtraSmall),
                    border = FilterChipDefaults.border(
                        border = Border(
                            border = BorderStroke(
                                width = 2.dp, color = MaterialTheme.colorScheme.border.copy(alpha = 0.5f)
                            ), shape = ShapeDefaults.ExtraSmall
                        ),
                        focusedBorder = Border(
                            border = BorderStroke(
                                width = 2.dp,
                                color = Color.White,
                            ), shape = ShapeDefaults.ExtraSmall
                        ),
                    ),
                    colors = FilterChipDefaults.colors(
                        focusedContainerColor = Color.Transparent,
                        selectedContainerColor = ChipColor,
                        focusedSelectedContainerColor = ChipColor,
                        focusedContentColor = Color.White,
                        focusedSelectedContentColor = ChipContentColor
                    ),
                    scale = FilterChipDefaults.scale(focusedScale = 1f),
                    selected = false, onClick = { }) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            "$text",
                            style = MaterialTheme.typography.titleMedium.copy(
                                color = Color.White,
                                fontSize = 32.sp
                            )
                        )
                    }
                }
            }
        }
    }
}

private val ChipColor @Composable get() = Color.White.copy(alpha = 0.1f)
private val ChipContentColor @Composable get() = MaterialTheme.colorScheme.inverseSurface

// 沉浸式列表增加蒙层
fun Modifier.gradientOverlay(gradientColor: Color) = this then drawWithCache {
    val horizontalGradient = Brush.horizontalGradient(
        colors = listOf(
            gradientColor,
            Color.Transparent
        ),
        startX = size.width.times(0.0f),
        endX = size.width.times(0.1f)
    )
    val verticalGradient = Brush.verticalGradient(
        colors = listOf(
            Color.Transparent,
            gradientColor
        ),
        endY = size.width.times(0.3f)
    )
    val linearGradient = Brush.linearGradient(
        colors = listOf(
            gradientColor,
            Color.Transparent
        ),
        start = Offset(
            size.width.times(0.1f),
            size.height.times(0.3f)
        ),
        end = Offset(
            size.width.times(0.7f),
            0f
        )
    )

    onDrawWithContent {
        drawContent()
        // drawRect(horizontalGradient)
        //drawRect(verticalGradient)
        drawRect(linearGradient)
    }
}

@Composable
private fun LoadingContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 使用简单的文本代替CircularProgressIndicator
            Text(
                text = "⏳",
                style = MaterialTheme.typography.displayLarge
            )
            Text(
                text = "正在加载内容...",
                style = MaterialTheme.typography.bodyLarge,
                color = Color.White
            )
        }
    }
}

@Composable
private fun ErrorContent(
    error: String,
    onRetry: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(24.dp),
            modifier = Modifier.padding(48.dp)
        ) {
            // 错误图标
            Text(
                text = "⚠️",
                style = MaterialTheme.typography.displayLarge
            )

            // 错误标题
            Text(
                text = "加载推荐内容失败",
                style = MaterialTheme.typography.headlineMedium,
                color = Color.White
            )

            // 错误详情
            Text(
                text = error,
                style = MaterialTheme.typography.bodyLarge,
                color = Color.White.copy(alpha = 0.75f),
                textAlign = TextAlign.Center
            )

            // 重试按钮
            Button(
                onClick = onRetry,
                colors = ButtonDefaults.colors(
                    containerColor = Color.White.copy(alpha = 0.2f)
                ),
                modifier = Modifier.padding(top = 8.dp)
            ) {
                Text(
                    text = "重新加载",
                    style = MaterialTheme.typography.labelLarge,
                    color = Color.White
                )
            }

            // 提示信息
            Text(
                text = "请检查网络连接或稍后重试",
                style = MaterialTheme.typography.bodySmall,
                color = Color.White.copy(alpha = 0.6f)
            )
        }
    }
}

@Composable
private fun SuccessContent(
    uiState: MovieUiState,
    tvLazyListState: androidx.tv.foundation.lazy.list.TvLazyListState
) {
    TvLazyColumn(
        state = tvLazyListState,
        modifier = Modifier.fillMaxSize()
    ) {
        // 缓存状态指示器（仅在调试时显示）
        if (uiState.isFromCache) {
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.TopEnd
                ) {
                    Text(
                        text = "📱 缓存数据",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Yellow.copy(alpha = 0.8f),
                        modifier = Modifier
                            .padding(8.dp)
                    )
                }
            }
        }
        // 轮播图 - 使用推荐内容或banner数据
        item {
            val bannerData =
                (uiState.recommendMovies.take(3)+ uiState.recommendTvShows.take(3))
            bannerData.forEach { data -> data.url=data.bgUrl }

            if (bannerData.isNotEmpty()) {
                MovicesImmersList(bannerData)
            }
        }

        // 热门电影
        if (uiState.recommendMovies.isNotEmpty()) {
            item {
                MoviesLazyRow("🎬 热门电影", uiState.recommendMovies.drop(3))
            }
        }

        // 分类筛选
        item {
            MovicesColumnItem()
        }

        // 热门剧集
        if (uiState.recommendTvShows.isNotEmpty()) {
            item {
                MoviesLazyRow("📺 热门剧集", uiState.recommendTvShows.drop(3))
            }
        }

        // 推荐动漫
        if (uiState.recommendAnime.isNotEmpty()) {
            item {
                MoviesLazyRow("🎌 推荐动漫", uiState.recommendAnime, contentPadding = 30.dp, itemWidth = 412.dp, itemHeight = 200.dp)
            }
        }

        // 推荐综艺
        if (uiState.recommendVariety.isNotEmpty()) {
            item {
                MoviesLazyRow("🎪 推荐综艺", uiState.recommendVariety)
            }
        }

        // 推荐纪录片
        if (uiState.recommendDocumentary.isNotEmpty()) {
            item {
                MoviesLazyRow("📖 推荐纪录片", uiState.recommendDocumentary)
            }
        }

        // 推荐短剧
        if (uiState.recommendShort.isNotEmpty()) {
            item {
                MoviesLazyRow("⚡ 推荐短剧", uiState.recommendShort)
            }
        }

        item {
            Spacer(
                modifier = Modifier.padding(
                    bottom = LocalConfiguration.current.screenWidthDp.dp.times(0.19f)
                )
            )
        }
    }
}

