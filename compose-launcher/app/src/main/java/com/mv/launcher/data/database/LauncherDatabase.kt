package com.mv.launcher.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import android.content.Context
import com.mv.launcher.data.database.dao.CachedVideoDao
import com.mv.launcher.data.database.entity.CachedVideoEntity

@Database(
    entities = [
        CachedVideoEntity::class
    ],
    version = 1,
    exportSchema = false
)
abstract class LauncherDatabase : RoomDatabase() {
    
    abstract fun cachedVideoDao(): CachedVideoDao
    
    companion object {
        const val DATABASE_NAME = "launcher_database"
        
        @Volatile
        private var INSTANCE: LauncherDatabase? = null
        
        fun getDatabase(context: Context): LauncherDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    LauncherDatabase::class.java,
                    DATABASE_NAME
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
