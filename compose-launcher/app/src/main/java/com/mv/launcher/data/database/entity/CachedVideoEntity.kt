package com.mv.launcher.data.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.mv.launcher.bean.MoviceData
import com.mv.launcher.data.model.Video

@Entity(tableName = "cached_videos")
data class CachedVideoEntity(
    @PrimaryKey
    val id: Int,
    val title: String?,
    val pic: String?,
    val year: Int?,
    val content: String?,
    val director: String?,
    val starring: String?,
    val area: String?,
    val language: String?,
    val typeId: Int?,
    val remark: String?,
    val subTitle: String?,
    val tags: String?,
    val createTime: String?,
    val updateTime: String?,
    val doubanId: String?,
    val imdbId: String?,
    val tmdbId: Int?,
    val backdropPath: String?,
    val index: Int?,
    val rating: Double?,
    val cacheTime: Long, // 缓存时间戳
    val cacheType: String // 缓存类型：recommend, movie, tv_show, anime, variety, documentary, short
)

/**
 * 将Video转换为CachedVideoEntity
 */
fun Video.toCachedEntity(cacheType: String): CachedVideoEntity {
    return CachedVideoEntity(
        id = this.id,
        title = this.title,
        pic = this.pic,
        year = this.year,
        content = this.content,
        director = this.director,
        starring = this.starring,
        area = this.area,
        language = this.language,
        typeId = this.typeId,
        remark = this.remark,
        subTitle = this.subTitle,
        tags = this.tags,
        createTime = this.createTime,
        updateTime = this.updateTime,
        doubanId = this.doubanId,
        imdbId = this.imdbId,
        tmdbId = this.tmdbId,
        backdropPath = this.backdropPath,
        index = this.index,
        rating = this.rating,
        cacheTime = System.currentTimeMillis(),
        cacheType = cacheType
    )
}

/**
 * 将CachedVideoEntity转换为Video
 */
fun CachedVideoEntity.toVideo(): Video {
    return Video(
        id = this.id,
        title = this.title,
        pic = this.pic,
        year = this.year,
        content = this.content,
        director = this.director,
        starring = this.starring,
        area = this.area,
        language = this.language,
        typeId = this.typeId,
        remark = this.remark,
        subTitle = this.subTitle,
        tags = this.tags,
        createTime = this.createTime,
        updateTime = this.updateTime,
        doubanId = this.doubanId,
        imdbId = this.imdbId,
        tmdbId = this.tmdbId,
        backdropPath = this.backdropPath,
        index = this.index,
        rating = this.rating
    )
}

/**
 * 缓存类型常量
 */
object CacheType {
    const val RECOMMEND = "recommend"
    const val MOVIE = "movie"
    const val TV_SHOW = "tv_show"
    const val ANIME = "anime"
    const val VARIETY = "variety"
    const val DOCUMENTARY = "documentary"
    const val SHORT = "short"
}
