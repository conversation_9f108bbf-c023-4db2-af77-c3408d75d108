package com.mv.launcher.data.repository

import com.mv.launcher.data.cache.CacheConfig
import com.mv.launcher.data.database.dao.CachedVideoDao
import com.mv.launcher.data.database.entity.CachedVideoEntity
import com.mv.launcher.data.database.entity.CacheType
import com.mv.launcher.data.database.entity.toCachedEntity
import com.mv.launcher.data.database.entity.toVideo
import com.mv.launcher.data.model.Video
import com.mv.launcher.utils.KLog
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CacheRepository @Inject constructor(
    private val cachedVideoDao: CachedVideoDao
) {
    
    /**
     * 获取缓存的推荐视频
     */
    suspend fun getCachedRecommendVideos(): List<Video> {
        return try {
            val cachedVideos = cachedVideoDao.getVideosByType(CacheType.RECOMMEND)
            KLog.d("获取缓存推荐视频: ${cachedVideos.size}条")
            cachedVideos.map { it.toVideo() }
        } catch (e: Exception) {
            KLog.e("获取缓存推荐视频失败: ${e.message}")
            emptyList()
        }
    }
    
    /**
     * 缓存推荐视频
     */
    suspend fun cacheRecommendVideos(videos: List<Video>) {
        try {
            // 先清除旧的推荐缓存
            cachedVideoDao.deleteVideosByType(CacheType.RECOMMEND)
            
            // 插入新的缓存数据
            val cachedEntities = videos.map { it.toCachedEntity(CacheType.RECOMMEND) }
            cachedVideoDao.insertVideos(cachedEntities)
            
            KLog.d("缓存推荐视频成功: ${videos.size}条")
        } catch (e: Exception) {
            KLog.e("缓存推荐视频失败: ${e.message}")
        }
    }
    
    /**
     * 检查推荐视频缓存是否有效
     */
    suspend fun isRecommendCacheValid(): Boolean {
        return try {
            val expireTime = CacheConfig.getExpireTimestamp(CacheConfig.RECOMMEND_CACHE_EXPIRE_TIME)
            val isValid = cachedVideoDao.isCacheValid(CacheType.RECOMMEND, expireTime)
            KLog.d("推荐视频缓存有效性: $isValid")
            isValid
        } catch (e: Exception) {
            KLog.e("检查推荐视频缓存有效性失败: ${e.message}")
            false
        }
    }
    
    /**
     * 根据类型获取缓存视频
     */
    suspend fun getCachedVideosByType(typeId: Int): List<Video> {
        return try {
            val cacheType = when (typeId) {
                1 -> CacheType.MOVIE
                2 -> CacheType.TV_SHOW
                3 -> CacheType.ANIME
                4 -> CacheType.VARIETY
                5 -> CacheType.DOCUMENTARY
                7 -> CacheType.SHORT
                else -> return emptyList()
            }
            
            val cachedVideos = cachedVideoDao.getVideosByType(cacheType)
            KLog.d("获取缓存视频(类型$typeId): ${cachedVideos.size}条")
            cachedVideos.map { it.toVideo() }
        } catch (e: Exception) {
            KLog.e("获取缓存视频失败: ${e.message}")
            emptyList()
        }
    }
    
    /**
     * 根据类型缓存视频
     */
    suspend fun cacheVideosByType(videos: List<Video>, typeId: Int) {
        try {
            val cacheType = when (typeId) {
                1 -> CacheType.MOVIE
                2 -> CacheType.TV_SHOW
                3 -> CacheType.ANIME
                4 -> CacheType.VARIETY
                5 -> CacheType.DOCUMENTARY
                7 -> CacheType.SHORT
                else -> return
            }
            
            // 先清除旧的缓存
            cachedVideoDao.deleteVideosByType(cacheType)
            
            // 插入新的缓存数据
            val cachedEntities = videos.map { it.toCachedEntity(cacheType) }
            cachedVideoDao.insertVideos(cachedEntities)
            
            KLog.d("缓存视频成功(类型$typeId): ${videos.size}条")
        } catch (e: Exception) {
            KLog.e("缓存视频失败: ${e.message}")
        }
    }
    
    /**
     * 检查指定类型的缓存是否有效
     */
    suspend fun isCacheValidByType(typeId: Int): Boolean {
        return try {
            val cacheType = when (typeId) {
                1 -> CacheType.MOVIE
                2 -> CacheType.TV_SHOW
                3 -> CacheType.ANIME
                4 -> CacheType.VARIETY
                5 -> CacheType.DOCUMENTARY
                7 -> CacheType.SHORT
                else -> return false
            }
            
            val expireTime = CacheConfig.getExpireTimestamp()
            val isValid = cachedVideoDao.isCacheValid(cacheType, expireTime)
            KLog.d("视频缓存有效性(类型$typeId): $isValid")
            isValid
        } catch (e: Exception) {
            KLog.e("检查视频缓存有效性失败: ${e.message}")
            false
        }
    }
    
    /**
     * 清理过期缓存
     */
    suspend fun cleanExpiredCache() {
        try {
            val expireTime = CacheConfig.getExpireTimestamp()
            cachedVideoDao.deleteExpiredVideos(expireTime)
            KLog.d("清理过期缓存完成")
        } catch (e: Exception) {
            KLog.e("清理过期缓存失败: ${e.message}")
        }
    }
    
    /**
     * 清理所有缓存
     */
    suspend fun clearAllCache() {
        try {
            cachedVideoDao.deleteAllVideos()
            KLog.d("清理所有缓存完成")
        } catch (e: Exception) {
            KLog.e("清理所有缓存失败: ${e.message}")
        }
    }
}
