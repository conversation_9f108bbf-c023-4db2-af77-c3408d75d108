package com.mv.launcher.data.repository

import com.mv.launcher.data.api.ApiService
import com.mv.launcher.data.model.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class VideoRepository @Inject constructor(
    private val apiService: ApiService
) {
    
    /**
     * 获取推荐视频
     */
    suspend fun getRecommendVideos(): Result<List<Video>> = try {
        val response = apiService.getRecommendVideos()
        if (response.isSuccessful && response.body()?.success == true) {
            val videos = response.body()?.data ?: emptyList()
            Result.success(videos)
        } else {
            Result.failure(Exception(response.body()?.message ?: "获取推荐视频失败"))
        }
    } catch (e: Exception) {
        Result.failure(e)
    }
    
    /**
     * 获取首页数据（轮播图等）
     */
    suspend fun getHomeData(): Result<HomeData> = try {
        val response = apiService.getHomeData()
        if (response.isSuccessful && response.body()?.success == true) {
            val homeData = response.body()?.data
            if (homeData != null) {
                Result.success(homeData)
            } else {
                Result.failure(Exception("首页数据为空"))
            }
        } else {
            Result.failure(Exception(response.body()?.message ?: "获取首页数据失败"))
        }
    } catch (e: Exception) {
        Result.failure(e)
    }
    
    /**
     * 获取指定分类的视频列表
     */
    suspend fun getCategoryVideos(
        typeId: Int,
        page: Int = 1,
        pageSize: Int = 20,
        area: String? = null,
        year: Int? = null
    ): Result<List<Video>> = try {
        val response = apiService.getVideoList(
            page = page,
            pageSize = pageSize,
            typeId = typeId,
            area = area,
            year = year
        )
        
        if (response.isSuccessful && response.body()?.success == true) {
            val videos = response.body()?.data?.items ?: emptyList()
            Result.success(videos)
        } else {
            Result.failure(Exception(response.body()?.message ?: "获取分类视频失败"))
        }
    } catch (e: Exception) {
        Result.failure(e)
    }
    
    /**
     * 搜索视频
     */
    suspend fun searchVideos(
        query: String,
        page: Int = 1,
        pageSize: Int = 20
    ): Result<List<Video>> = try {
        val response = apiService.searchVideos(
            query = query,
            page = page,
            pageSize = pageSize
        )
        
        if (response.isSuccessful && response.body()?.success == true) {
            val videos = response.body()?.data?.items ?: emptyList()
            Result.success(videos)
        } else {
            Result.failure(Exception(response.body()?.message ?: "搜索失败"))
        }
    } catch (e: Exception) {
        Result.failure(e)
    }
    
    /**
     * 获取视频详情
     */
    suspend fun getVideoDetail(id: Int): Result<Video> = try {
        val response = apiService.getVideoDetail(id)
        
        if (response.isSuccessful && response.body()?.success == true) {
            val video = response.body()?.data
            if (video != null) {
                Result.success(video)
            } else {
                Result.failure(Exception("视频不存在"))
            }
        } else {
            Result.failure(Exception(response.body()?.message ?: "获取视频详情失败"))
        }
    } catch (e: Exception) {
        Result.failure(e)
    }
    
    /**
     * 获取分类列表
     */
    fun getCategories(): List<VideoCategory> {
        return listOf(
            VideoCategory(1, "电影"),
            VideoCategory(2, "电视剧"),
            VideoCategory(3, "动漫"),
            VideoCategory(4, "综艺"),
            VideoCategory(5, "纪录片"),
            VideoCategory(7, "短剧")
        )
    }
}
