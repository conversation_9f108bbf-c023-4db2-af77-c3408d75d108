package com.mv.launcher.data.repository

import com.mv.launcher.data.api.ApiService
import com.mv.launcher.data.model.*
import com.mv.launcher.utils.KLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class VideoRepository @Inject constructor(
    private val apiService: ApiService,
    private val cacheRepository: CacheRepository
) {
    
    /**
     * 获取推荐视频（支持缓存）
     * 先返回缓存数据，然后异步更新缓存
     */
    suspend fun getRecommendVideos(): Result<List<Video>> = withContext(Dispatchers.IO) {
        try {
            // 1. 先检查缓存是否有效
            val isCacheValid = cacheRepository.isRecommendCacheValid()

            if (isCacheValid) {
                // 缓存有效，直接返回缓存数据
                val cachedVideos = cacheRepository.getCachedRecommendVideos()
                if (cachedVideos.isNotEmpty()) {
                    KLog.d("返回缓存的推荐视频: ${cachedVideos.size}条")
                    return@withContext Result.success(cachedVideos)
                }
            }

            // 2. 缓存无效或为空，请求网络数据
            KLog.d("缓存无效或为空，请求网络数据")
            val response = apiService.getRecommendVideos()

            if (response.isSuccessful && response.body()?.success == true) {
                val videos = response.body()?.data ?: emptyList()

                // 3. 缓存新数据
                if (videos.isNotEmpty()) {
                    cacheRepository.cacheRecommendVideos(videos)
                    KLog.d("网络请求成功，已缓存推荐视频: ${videos.size}条")
                }

                Result.success(videos)
            } else {
                // 4. 网络请求失败，尝试返回缓存数据
                val cachedVideos = cacheRepository.getCachedRecommendVideos()
                if (cachedVideos.isNotEmpty()) {
                    KLog.d("网络请求失败，返回缓存数据: ${cachedVideos.size}条")
                    Result.success(cachedVideos)
                } else {
                    Result.failure(Exception(response.body()?.message ?: "获取推荐视频失败"))
                }
            }
        } catch (e: Exception) {
            // 5. 异常情况，尝试返回缓存数据
            KLog.e("获取推荐视频异常: ${e.message}")
            val cachedVideos = cacheRepository.getCachedRecommendVideos()
            if (cachedVideos.isNotEmpty()) {
                KLog.d("异常情况，返回缓存数据: ${cachedVideos.size}条")
                Result.success(cachedVideos)
            } else {
                Result.failure(e)
            }
        }
    }

    /**
     * 获取推荐视频（缓存优先策略）
     * 立即返回缓存数据，同时在后台更新缓存
     */
    suspend fun getRecommendVideosWithCache(onBackgroundUpdate: suspend (List<Video>) -> Unit = {}): Pair<List<Video>, Boolean> = withContext(Dispatchers.IO) {
        try {
            // 1. 先获取缓存数据
            val cachedVideos = cacheRepository.getCachedRecommendVideos()
            val isCacheValid = cacheRepository.isRecommendCacheValid()

            // 2. 如果有缓存数据，立即返回
            if (cachedVideos.isNotEmpty()) {
                KLog.d("返回缓存数据: ${cachedVideos.size}条，缓存有效: $isCacheValid")

                // 3. 如果缓存无效，在后台更新缓存
                if (!isCacheValid) {
                    // 启动后台更新任务
                    launch {
                        try {
                            KLog.d("开始后台更新缓存")
                            val response = apiService.getRecommendVideos()
                            if (response.isSuccessful && response.body()?.success == true) {
                                val newVideos = response.body()?.data ?: emptyList()
                                if (newVideos.isNotEmpty()) {
                                    cacheRepository.cacheRecommendVideos(newVideos)
                                    KLog.d("后台更新缓存成功: ${newVideos.size}条")
                                    // 通知UI更新
                                    onBackgroundUpdate(newVideos)
                                }
                            }
                        } catch (e: Exception) {
                            KLog.e("后台更新缓存失败: ${e.message}")
                        }
                    }
                }

                return@withContext Pair(cachedVideos, isCacheValid)
            }

            // 4. 没有缓存数据，直接请求网络
            KLog.d("没有缓存数据，直接请求网络")
            val response = apiService.getRecommendVideos()
            if (response.isSuccessful && response.body()?.success == true) {
                val videos = response.body()?.data ?: emptyList()
                if (videos.isNotEmpty()) {
                    cacheRepository.cacheRecommendVideos(videos)
                    KLog.d("首次加载成功: ${videos.size}条")
                }
                return@withContext Pair(videos, true)
            } else {
                return@withContext Pair(emptyList(), false)
            }
        } catch (e: Exception) {
            KLog.e("获取推荐视频异常: ${e.message}")
            return@withContext Pair(emptyList(), false)
        }
    }

    /**
     * 获取首页数据（轮播图等）
     */
    suspend fun getHomeData(): Result<HomeData> = try {
        val response = apiService.getHomeData()
        if (response.isSuccessful && response.body()?.success == true) {
            val homeData = response.body()?.data
            if (homeData != null) {
                Result.success(homeData)
            } else {
                Result.failure(Exception("首页数据为空"))
            }
        } else {
            Result.failure(Exception(response.body()?.message ?: "获取首页数据失败"))
        }
    } catch (e: Exception) {
        Result.failure(e)
    }
    
    /**
     * 获取指定分类的视频列表
     */
    suspend fun getCategoryVideos(
        typeId: Int,
        page: Int = 1,
        pageSize: Int = 20,
        area: String? = null,
        year: Int? = null
    ): Result<List<Video>> = try {
        val response = apiService.getVideoList(
            page = page,
            pageSize = pageSize,
            typeId = typeId,
            area = area,
            year = year
        )
        
        if (response.isSuccessful && response.body()?.success == true) {
            val videos = response.body()?.data?.items ?: emptyList()
            Result.success(videos)
        } else {
            Result.failure(Exception(response.body()?.message ?: "获取分类视频失败"))
        }
    } catch (e: Exception) {
        Result.failure(e)
    }
    
    /**
     * 搜索视频
     */
    suspend fun searchVideos(
        query: String,
        page: Int = 1,
        pageSize: Int = 20
    ): Result<List<Video>> = try {
        val response = apiService.searchVideos(
            query = query,
            page = page,
            pageSize = pageSize
        )
        
        if (response.isSuccessful && response.body()?.success == true) {
            val videos = response.body()?.data?.items ?: emptyList()
            Result.success(videos)
        } else {
            Result.failure(Exception(response.body()?.message ?: "搜索失败"))
        }
    } catch (e: Exception) {
        Result.failure(e)
    }
    
    /**
     * 获取视频详情
     */
    suspend fun getVideoDetail(id: Int): Result<Video> = try {
        val response = apiService.getVideoDetail(id)
        
        if (response.isSuccessful && response.body()?.success == true) {
            val video = response.body()?.data
            if (video != null) {
                Result.success(video)
            } else {
                Result.failure(Exception("视频不存在"))
            }
        } else {
            Result.failure(Exception(response.body()?.message ?: "获取视频详情失败"))
        }
    } catch (e: Exception) {
        Result.failure(e)
    }
    
    /**
     * 获取分类列表
     */
    fun getCategories(): List<VideoCategory> {
        return listOf(
            VideoCategory(1, "电影"),
            VideoCategory(2, "电视剧"),
            VideoCategory(3, "动漫"),
            VideoCategory(4, "综艺"),
            VideoCategory(5, "纪录片"),
            VideoCategory(7, "短剧")
        )
    }
}
