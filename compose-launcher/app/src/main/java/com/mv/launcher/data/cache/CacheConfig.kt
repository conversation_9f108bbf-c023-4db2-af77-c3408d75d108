package com.mv.launcher.data.cache

/**
 * 缓存配置
 */
object CacheConfig {
    
    /**
     * 缓存过期时间（毫秒）
     * 默认30分钟
     */
    const val CACHE_EXPIRE_TIME = 30 * 60 * 1000L
    
    /**
     * 推荐视频缓存过期时间（毫秒）
     * 默认15分钟，推荐内容更新频率较高
     */
    const val RECOMMEND_CACHE_EXPIRE_TIME = 15 * 60 * 1000L
    
    /**
     * 最大缓存数量
     */
    const val MAX_CACHE_COUNT = 1000
    
    /**
     * 每种类型最大缓存数量
     */
    const val MAX_CACHE_COUNT_PER_TYPE = 200
    
    /**
     * 检查缓存是否过期
     * @param cacheTime 缓存时间戳
     * @param expireTime 过期时间（毫秒）
     * @return true表示缓存有效，false表示已过期
     */
    fun isCacheValid(cacheTime: Long, expireTime: Long = CACHE_EXPIRE_TIME): Boolean {
        return System.currentTimeMillis() - cacheTime < expireTime
    }
    
    /**
     * 获取过期时间戳
     * @param expireTime 过期时间（毫秒）
     * @return 过期时间戳
     */
    fun getExpireTimestamp(expireTime: Long = CACHE_EXPIRE_TIME): Long {
        return System.currentTimeMillis() - expireTime
    }
}
