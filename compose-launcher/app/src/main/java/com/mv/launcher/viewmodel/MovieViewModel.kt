package com.mv.launcher.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mv.launcher.bean.MoviceData
import com.mv.launcher.data.model.toMoviceData
import com.mv.launcher.data.repository.VideoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MovieViewModel @Inject constructor(
    private val repository: VideoRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(MovieUiState())
    val uiState: StateFlow<MovieUiState> = _uiState.asStateFlow()

    init {
        loadMovieData()
    }

    private fun loadMovieData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                val recommendResult = repository.getRecommendVideos()
                
                recommendResult.fold(
                    onSuccess = { recommendVideos ->
                        // 从推荐视频中筛选电影和剧集
                        val recommendMovies = recommendVideos
                            .filter { it.typeId == 1 }
                            .map { it.toMoviceData() }
                        
                        val recommendTvShows = recommendVideos
                            .filter { it.typeId == 2 }
                            .map { it.toMoviceData() }
                        
                        val recommendAnime = recommendVideos
                            .filter { it.typeId == 3 }
                            .map { it.toMoviceData() }
                        
                        val recommendVariety = recommendVideos
                            .filter { it.typeId == 4 }
                            .map { it.toMoviceData() }
                        
                        val recommendDocumentary = recommendVideos
                            .filter { it.typeId == 5 }
                            .map { it.toMoviceData() }
                        
                        val recommendShort = recommendVideos
                            .filter { it.typeId == 7 }
                            .map { it.toMoviceData() }

                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            recommendMovies = recommendMovies,
                            recommendTvShows = recommendTvShows,
                            recommendAnime = recommendAnime,
                            recommendVariety = recommendVariety,
                            recommendDocumentary = recommendDocumentary,
                            recommendShort = recommendShort,
                            error = null
                        )
                    },
                    onFailure = { exception ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = exception.message ?: "加载推荐数据失败"
                        )
                    }
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载数据失败"
                )
            }
        }
    }

    fun refresh() {
        loadMovieData()
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    fun retryLoadData() {
        loadMovieData()
    }
}

data class MovieUiState(
    val isLoading: Boolean = true,
    val bannerMovies: List<MoviceData> = emptyList(),
    val recommendMovies: List<MoviceData> = emptyList(),
    val recommendTvShows: List<MoviceData> = emptyList(),
    val recommendAnime: List<MoviceData> = emptyList(),
    val recommendVariety: List<MoviceData> = emptyList(),
    val recommendDocumentary: List<MoviceData> = emptyList(),
    val recommendShort: List<MoviceData> = emptyList(),
    val error: String? = null
)
