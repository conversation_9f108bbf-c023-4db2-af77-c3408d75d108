package com.mv.launcher.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mv.launcher.bean.MoviceData
import com.mv.launcher.data.model.toMoviceData
import com.mv.launcher.data.repository.VideoRepository
import com.mv.launcher.utils.KLog
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MovieViewModel @Inject constructor(
    private val repository: VideoRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(MovieUiState())
    val uiState: StateFlow<MovieUiState> = _uiState.asStateFlow()

    init {
        loadMovieData()
    }

    private fun loadMovieData() {
        viewModelScope.launch {
            try {
                KLog.d("开始加载电影数据")

                // 使用缓存优先策略
                val (cachedVideos, isCacheValid) = repository.getRecommendVideosWithCache()

                if (cachedVideos.isNotEmpty()) {
                    // 有缓存数据，立即显示
                    KLog.d("显示缓存数据: ${cachedVideos.size}条，缓存有效: $isCacheValid")
                    updateUiStateWithVideos(cachedVideos, isFromCache = true, isCacheValid = isCacheValid)
                } else {
                    // 没有缓存数据，显示加载状态
                    _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                }

                // 如果缓存无效或没有缓存，请求最新数据
                if (!isCacheValid || cachedVideos.isEmpty()) {
                    KLog.d("缓存无效或为空，请求最新数据")
                    val recommendResult = repository.getRecommendVideos()

                    recommendResult.fold(
                        onSuccess = { recommendVideos ->
                            KLog.d("网络请求成功: ${recommendVideos.size}条")
                            updateUiStateWithVideos(recommendVideos, isFromCache = false, isCacheValid = true)
                        },
                        onFailure = { exception ->
                            KLog.e("网络请求失败: ${exception.message}")
                            // 如果之前没有显示缓存数据，才显示错误
                            if (cachedVideos.isEmpty()) {
                                _uiState.value = _uiState.value.copy(
                                    isLoading = false,
                                    error = exception.message ?: "加载推荐数据失败"
                                )
                            }
                        }
                    )
                }
            } catch (e: Exception) {
                KLog.e("加载数据异常: ${e.message}")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载数据失败"
                )
            }
        }
    }

    /**
     * 根据视频数据更新UI状态
     */
    private fun updateUiStateWithVideos(videos: List<com.mv.launcher.data.model.Video>, isFromCache: Boolean, isCacheValid: Boolean) {
        // 从推荐视频中筛选不同类型
        val recommendMovies = videos
            .filter { it.typeId == 1 }
            .map { it.toMoviceData() }

        val recommendTvShows = videos
            .filter { it.typeId == 2 }
            .map { it.toMoviceData() }

        val recommendAnime = videos
            .filter { it.typeId == 3 }
            .map { it.toMoviceData() }

        val recommendVariety = videos
            .filter { it.typeId == 4 }
            .map { it.toMoviceData() }

        val recommendDocumentary = videos
            .filter { it.typeId == 5 }
            .map { it.toMoviceData() }

        val recommendShort = videos
            .filter { it.typeId == 7 }
            .map { it.toMoviceData() }

        _uiState.value = _uiState.value.copy(
            isLoading = false,
            recommendMovies = recommendMovies,
            recommendTvShows = recommendTvShows,
            recommendAnime = recommendAnime,
            recommendVariety = recommendVariety,
            recommendDocumentary = recommendDocumentary,
            recommendShort = recommendShort,
            error = null,
            isFromCache = isFromCache,
            lastUpdateTime = System.currentTimeMillis()
        )

        KLog.d("UI状态更新完成 - 电影:${recommendMovies.size}, 剧集:${recommendTvShows.size}, 动漫:${recommendAnime.size}, 综艺:${recommendVariety.size}, 纪录片:${recommendDocumentary.size}, 短剧:${recommendShort.size}")
    }

    fun refresh() {
        loadMovieData()
    }

    /**
     * 强制刷新数据（忽略缓存）
     */
    fun forceRefresh() {
        viewModelScope.launch {
            try {
                KLog.d("强制刷新数据")
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)

                val recommendResult = repository.getRecommendVideos()

                recommendResult.fold(
                    onSuccess = { recommendVideos ->
                        KLog.d("强制刷新成功: ${recommendVideos.size}条")
                        updateUiStateWithVideos(recommendVideos, isFromCache = false, isCacheValid = true)
                    },
                    onFailure = { exception ->
                        KLog.e("强制刷新失败: ${exception.message}")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = exception.message ?: "刷新数据失败"
                        )
                    }
                )
            } catch (e: Exception) {
                KLog.e("强制刷新异常: ${e.message}")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "刷新数据失败"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun retryLoadData() {
        loadMovieData()
    }
}

data class MovieUiState(
    val isLoading: Boolean = true,
    val bannerMovies: List<MoviceData> = emptyList(),
    val recommendMovies: List<MoviceData> = emptyList(),
    val recommendTvShows: List<MoviceData> = emptyList(),
    val recommendAnime: List<MoviceData> = emptyList(),
    val recommendVariety: List<MoviceData> = emptyList(),
    val recommendDocumentary: List<MoviceData> = emptyList(),
    val recommendShort: List<MoviceData> = emptyList(),
    val error: String? = null,
    val isFromCache: Boolean = false, // 数据是否来自缓存
    val lastUpdateTime: Long = 0L // 最后更新时间
)
