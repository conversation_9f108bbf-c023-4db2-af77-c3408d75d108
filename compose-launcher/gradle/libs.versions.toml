[versions]
agp = "8.10.0"
kotlin = "2.0.21"
# lib version
core-ktx = "1.11.0-beta02"
compose-bom = "2023.08.00"
lifecycle-runtime-ktx = "2.6.1"
compose-ui = "1.6.0-SNAPSHOT"
activity-compose = "1.7.2"
navigation-compose = "2.6.0"
compose-for-tv = "1.0.0-alpha08"
# plugin version
#android-gradle-plugin = "8.1.0"
#kotlin-android = "1.8.10"

[libraries]
androidx-lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycle-runtime-ktx" }
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "core-ktx" }
androidx-lifecycle-runtime-compose = { module = "androidx.lifecycle:lifecycle-runtime-compose", version.ref = "lifecycle-runtime-ktx" }
#
androidx-compose-bom = { module = "androidx.compose:compose-bom", version.ref = "compose-bom" }
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "activity-compose" }
# Compose Navigation
androidx-navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref = "navigation-compose" }
# Compose UI libs (Using snapshot build for focus restoring APIs)
androidx-compose-ui-base = { module = "androidx.compose.ui:ui", version.ref = "compose-ui" }
androidx-compose-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview", version.ref = "compose-ui" }
# extra material icons
androidx-material-icons-extended = { module = "androidx.compose.material:material-icons-extended" }
# ViewModel in Compose
androidx-lifecycle-viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "lifecycle-runtime-ktx" }
# TV Compose
androidx-tv-foundation = { module = "androidx.tv:tv-foundation", version.ref = "compose-for-tv" }
androidx-tv-material = { module = "androidx.tv:tv-material", version.ref = "compose-for-tv" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
