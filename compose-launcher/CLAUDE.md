# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Android TV launcher application built with Jetpack Compose for TV. The app provides a TV-optimized interface with three main sections: "我的" (My), "影视" (Movies), and "应用" (Apps). The package name has been migrated from `com.open.launcher` to `com.mv.launcher`.

## Build Commands

- **Build the project**: `./gradlew build`
- **Run tests**: `./gradlew test`
- **Run instrumentation tests**: `./gradlew connectedAndroidTest`
- **Assemble debug APK**: `./gradlew assembleDebug`
- **Clean build**: `./gradlew clean`

## Architecture

### Core Structure
- **MainActivity.kt**: Main entry point with navigation setup, focus management, and custom density scaling
- **MyApp.kt**: Hilt Application class with Coil image loading configuration (GIF support, memory/disk caching)
- **Navigation**: Uses Jetpack Navigation Compose with three main destinations mapped to TopBarTabs
- **Screen Components**: Located in `screen/` package (AppScreen, MoviceScreen, MyScreen, TopBarScreen)

### Key Technologies
- **Jetpack Compose for TV**: UI framework with snapshot builds for focus restoration APIs
- **Hilt**: Dependency injection framework for modular architecture
- **Retrofit + OkHttp**: Network layer with logging interceptor
- **Coil**: Image loading with GIF support and optimized caching
- **Coroutines**: Asynchronous programming for network and UI operations
- **ViewModel + StateFlow**: State management following MVVM pattern

### Network & Data Layer
- **NetworkModule**: Provides Retrofit, OkHttpClient, and ApiService via Hilt
- **VideoRepository**: Handles API calls for video/movie data
- **MovieViewModel**: Manages UI state with StateFlow, categorizes content by typeId (1=movies, 2=TV shows, 3=anime, 4=variety, 5=documentary, 7=short videos)
- **Bean Classes**: Data models in `bean/` package with extension functions for data transformation

### UI Structure & Focus Management
- **Custom Density Scaling**: Scales UI based on 1920px width reference for consistent TV experience
- **Focus Navigation**: Uses FocusRequester to manage focus between top bar and content screens
- **D-pad Support**: Handles KEYCODE_DPAD_DOWN for remote control navigation
- **Three-tab System**: Navigation between "我的", "影视", and "应用" with visual indicators

### Data Flow
- Repository pattern with Result-based error handling
- ViewModel exposes StateFlow for reactive UI updates
- Content categorization by typeId for different media types
- Loading states, error handling, and retry mechanisms

## Development Notes

- **Target SDK**: 36, **Min SDK**: 24, **Java Version**: 17
- **Package Migration**: From `com.open.launcher` to `com.mv.launcher`
- **Hilt Integration**: All major components use dependency injection
- **API Integration**: Base URL configured as `http://49.235.64.166:3001/`
- **TV Optimization**: Uses TvLazyVerticalGrid and TV-specific Material components
- **Image Caching**: Coil configured with 25% memory cache and 2% disk cache