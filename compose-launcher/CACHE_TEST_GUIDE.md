# 缓存功能测试指南

## 功能概述

已成功实现接口数据缓存功能，实现"进入页面后先显示上次缓存数据，接口请求成功后更新成新数据"的需求。

## 测试步骤

### 1. 首次启动测试
1. 清除应用数据或首次安装
2. 启动应用，进入影视页面
3. **预期结果**：显示加载状态，然后显示从网络获取的数据
4. **观察**：右上角显示更新时间，无"📱 缓存数据"标识

### 2. 缓存读取测试
1. 关闭应用
2. 重新启动应用，进入影视页面
3. **预期结果**：立即显示上次缓存的数据
4. **观察**：右上角显示"📱 缓存数据"标识和上次更新时间

### 3. 异步更新测试
1. 确保缓存已过期（等待15分钟或修改缓存过期时间）
2. 启动应用，进入影视页面
3. **预期结果**：
   - 立即显示缓存数据（带"📱 缓存数据"标识）
   - 几秒后自动更新为最新数据（"📱 缓存数据"标识消失，更新时间改变）

### 4. 网络异常测试
1. 断开网络连接
2. 启动应用，进入影视页面
3. **预期结果**：显示缓存数据，不显示错误页面

## 日志观察

使用 `adb logcat | grep LauncherCache` 查看详细日志：

```bash
# 查看缓存相关日志
adb logcat | grep LauncherCache

# 关键日志信息：
# - "开始加载电影数据"
# - "显示缓存数据: X条，缓存有效: true/false"
# - "开始后台更新缓存"
# - "后台更新缓存成功: X条"
# - "收到后台更新数据: X条"
```

## 缓存配置

- **推荐视频缓存过期时间**：15分钟
- **其他类型缓存过期时间**：30分钟
- **缓存位置**：Room数据库 (`launcher_database`)
- **缓存表**：`cached_videos`

## 技术实现要点

1. **缓存优先策略**：先读取缓存，立即显示，然后异步更新
2. **后台更新**：缓存过期时在后台请求新数据并更新UI
3. **错误回退**：网络请求失败时自动使用缓存数据
4. **状态透明**：UI显示数据来源和更新时间

## 性能优化

- 使用协程进行异步操作
- Room数据库提供高效的本地存储
- 智能缓存过期机制
- 最小化网络请求

## 故障排除

### 如果看不到异步更新：
1. 检查缓存是否已过期（15分钟）
2. 查看日志确认后台更新是否触发
3. 确认网络连接正常
4. 检查API服务是否可用

### 如果缓存不生效：
1. 检查数据库是否正确创建
2. 查看日志确认缓存写入是否成功
3. 确认依赖注入配置正确

## 调试技巧

1. **修改缓存过期时间**：在 `CacheConfig.kt` 中调整 `RECOMMEND_CACHE_EXPIRE_TIME`
2. **清除缓存**：调用 `cacheRepository.clearAllCache()`
3. **强制刷新**：调用 `viewModel.forceRefresh()`
