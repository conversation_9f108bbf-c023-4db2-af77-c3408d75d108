{"name": "movie-site", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "cf-typegen": "wrangler types --env-interface CloudflareEnv ./cloudflare-env.d.ts", "db:generate": "drizzle-kit generate", "db:migrate": "tsx ./scripts/migrate.ts", "db:push": "drizzle-kit push:mysql"}, "dependencies": {"@opennextjs/cloudflare": "^1.0.4", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "artplayer": "^5.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.16.0", "hls.js": "^1.6.2", "lucide-react": "^0.511.0", "mysql2": "^3.14.1", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^22.15.24", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.1", "tailwindcss": "^4", "tsx": "^4.19.4", "tw-animate-css": "^1.3.0", "typescript": "^5", "wrangler": "^4.17.0"}}