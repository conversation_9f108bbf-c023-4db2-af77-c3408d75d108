-- ----------------------------
-- Table structure for cloud_files
-- ----------------------------
CREATE TABLE `cloud_files` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cloud_id` int(11) DEFAULT NULL COMMENT '云存储服务ID',
  `vod_url_id` int(11) DEFAULT NULL COMMENT '视频URL ID，关联vod_urls表',
  `fid` char(50) DEFAULT NULL COMMENT '文件唯一标识符',
  `pdir_fid` char(50) DEFAULT NULL COMMENT '父目录文件标识符',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  `file_type` int(11) DEFAULT NULL COMMENT '文件类型（1:文件夹, 2:文件）',
  `format_type` char(32) DEFAULT NULL COMMENT '文件格式类型',
  `duration` int(11) DEFAULT NULL COMMENT '视频时长（秒）',
  `fps` int(11) DEFAULT NULL COMMENT '视频帧率',
  `video_height` int(11) DEFAULT NULL COMMENT '视频高度（像素）',
  `video_width` int(11) DEFAULT NULL COMMENT '视频宽度（像素）',
  `video_max_resolution` char(10) DEFAULT NULL COMMENT '视频最大分辨率',
  `l_created_at` bigint(20) DEFAULT NULL COMMENT '创建时间戳',
  `l_updated_at` bigint(20) DEFAULT NULL COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  KEY `cloud_files_FK_0_0` (`vod_url_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='云存储文件信息表';

-- ----------------------------
-- Table structure for vod_down_list
-- ----------------------------
CREATE TABLE `vod_down_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `vod_info_id` int(11) DEFAULT NULL COMMENT '视频信息ID，关联vod_infos表',
  `site_id` tinyint(4) DEFAULT NULL COMMENT '站点ID',
  `site_vod_id` varchar(10) DEFAULT NULL COMMENT '站点视频ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `url` varchar(40) DEFAULT NULL COMMENT '下载链接URL',
  PRIMARY KEY (`id`),
  KEY `vod_info_id` (`vod_info_id`),
  KEY `ix_vod_down_list_url` (`url`),
  CONSTRAINT `vod_down_list_ibfk_1` FOREIGN KEY (`vod_info_id`) REFERENCES `vod_infos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='视频下载列表';

-- ----------------------------
-- Table structure for vod_infos
-- ----------------------------
CREATE TABLE `vod_infos` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(50) DEFAULT NULL COMMENT '视频标题',
  `director` varchar(128) DEFAULT NULL COMMENT '导演',
  `year` int(11) DEFAULT NULL COMMENT '发行年份',
  `content` text COMMENT '内容简介',
  `pic` varchar(256) DEFAULT NULL COMMENT '封面图片URL',
  `starring` varchar(512) DEFAULT NULL COMMENT '主演',
  `type_id` int(11) DEFAULT NULL COMMENT '视频类型ID(1:电影,2:电视剧,3:动漫,4:综艺,5:纪录片,7:短剧)',
  `remark` varchar(80) DEFAULT NULL COMMENT '备注信息',
  `sub_title` varchar(80) DEFAULT NULL COMMENT '副标题',
  `tags` varchar(50) DEFAULT NULL COMMENT '标签',
  `tags2` varchar(30) DEFAULT NULL COMMENT '次要标签',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `area` varchar(50) DEFAULT NULL COMMENT '地区',
  `language` varchar(50) DEFAULT NULL COMMENT '语言',
  `bianju` varchar(100) DEFAULT NULL COMMENT '编剧',
  `stime` varchar(100) DEFAULT NULL COMMENT '上映时间',
  `times` varchar(100) DEFAULT NULL COMMENT '片长',
  `ename` varchar(256) DEFAULT NULL COMMENT '英文名称',
  `douban_id` varchar(8) DEFAULT NULL COMMENT '豆瓣ID',
  `imdb_id` varchar(10) DEFAULT NULL COMMENT 'IMDB ID',
  `ro_id` varchar(100) DEFAULT NULL COMMENT '其他ID',
  `tmdb_id` int(11) DEFAULT NULL COMMENT 'TMDB ID',
  `backdrop_path` char(50) DEFAULT NULL COMMENT '背景图片路径',
  `is_deleted` bit(1) DEFAULT b'0' COMMENT '是否已删除（0:否, 1:是）',
  PRIMARY KEY (`id`),
  KEY `ix_title_year` (`title`,`year`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='视频信息表';

-- ----------------------------
-- Table structure for vod_play_list
-- ----------------------------
CREATE TABLE `vod_play_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `vod_info_id` int(11) DEFAULT NULL COMMENT '视频信息ID，关联vod_infos表',
  `site_id` tinyint(4) DEFAULT NULL COMMENT '站点ID',
  `site_vod_id` char(4) DEFAULT NULL COMMENT '站点视频ID',
  `line_id` tinyint(4) DEFAULT NULL COMMENT '线路ID',
  `episode` int(11) DEFAULT NULL COMMENT '集数',
  `url` varchar(256) DEFAULT NULL COMMENT '播放链接URL',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `vod_info_id` (`vod_info_id`),
  KEY `ix_vod_play_list_url` (`url`),
  KEY `ix_site_vod_id` (`site_vod_id`,`line_id`) USING BTREE,
  CONSTRAINT `vod_play_list_ibfk_1` FOREIGN KEY (`vod_info_id`) REFERENCES `vod_infos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='视频播放列表';

-- ----------------------------
-- Table structure for vod_site_ids
-- ----------------------------
CREATE TABLE `vod_site_ids` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `vod_info_id` int(11) NOT NULL COMMENT '视频信息ID，关联vod_infos表',
  `site_id` int(11) NOT NULL COMMENT '站点ID',
  `site_vod_id` varchar(255) DEFAULT NULL COMMENT '站点视频ID',
  `type_id` int(11) DEFAULT NULL COMMENT '类型ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `ix_site_id` (`site_id`,`site_vod_id`),
  KEY `ix_site_vod_id` (`site_vod_id`,`site_id`),
  KEY `vod_site_ids_FK_0_0` (`vod_info_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='视频站点ID关联表';

-- ----------------------------
-- Table structure for vod_urls
-- ----------------------------
CREATE TABLE `vod_urls` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cloudId` int(11) DEFAULT NULL COMMENT '云存储ID',
  `url` varchar(100) DEFAULT NULL COMMENT '视频URL',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `vod_info_id` int(11) DEFAULT NULL COMMENT '视频信息ID，关联vod_infos表',
  `check_status` int(11) DEFAULT '0' COMMENT '检查状态（0:未检查, 1:已检查）',
  `is_invalid` int(11) DEFAULT '0' COMMENT '是否无效（0:有效, 1:无效）',
  `site_id` int(11) DEFAULT NULL COMMENT '站点ID',
  `pwd` char(6) DEFAULT NULL COMMENT '访问密码',
  `stoken` char(50) DEFAULT NULL COMMENT '安全令牌',
  `invalid_reason` varchar(100) DEFAULT NULL COMMENT '无效原因',
  PRIMARY KEY (`id`),
  KEY `ix_url` (`url`),
  KEY `vod_urls_FK_0_0` (`vod_info_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='视频URL信息表';

CREATE TABLE `vod_pic_info` (
  `vod_info_id` int(11) NOT NULL, -- 视频信息ID
  `pic_url` varchar(255) DEFAULT NULL, -- 图片URL
  `save_path` varchar(255) DEFAULT NULL, -- 保存路径
  `is_downloaded` tinyint(1) DEFAULT '0', -- 是否已下载
  `create_time` datetime DEFAULT NULL, -- 创建时间
  `update_time` datetime DEFAULT NULL, -- 更新时间
  PRIMARY KEY (`vod_info_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频图片信息表';

CREATE TABLE `recent_hot` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vod_info_id` int(11) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `type_id` tinyint(4) DEFAULT NULL,
  `douban_id` int(11) DEFAULT NULL,
  `episodes_info` varchar(128) DEFAULT NULL,
  `rating` float DEFAULT NULL,
  `subtitle` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4;

CREATE FUNCTION `get_url`(cloudId INTEGER, url VARCHAR(100)) RETURNS varchar(255) CHARSET utf8mb4
    DETERMINISTIC
BEGIN
    IF cloudId = 1 THEN
        RETURN CONCAT('https://www.alipan.com/s/', url);
    ELSEIF cloudId = 2 THEN
        RETURN CONCAT('https://pan.quark.cn/s/', url);
    ELSEIF cloudId = 3 THEN
        RETURN CONCAT('https://drive.uc.cn/s/', url);
    ELSEIF cloudId = 4 THEN
        RETURN CONCAT('https://pan.baidu.com/s/', url);
    ELSEIF cloudId = 5 THEN
        RETURN CONCAT('https://pan.xunlei.com/s/', url);
    ELSEIF cloudId = 6 THEN
        RETURN CONCAT('https://www.123pan.com/s/', url);
    ELSEIF cloudId = 7 THEN
        RETURN CONCAT('https://115cdn.com/s/', url);
    ELSEIF cloudId = 8 THEN
        RETURN CONCAT('https://caiyun.139.com/m/i?', url);
    ELSEIF cloudId = 9 THEN
        RETURN CONCAT('https://cloud.189.cn/t/', url);
    ELSEIF cloudId = 10 THEN
        RETURN CONCAT('magnet:?xt=urn:btih:', url);
    ELSE
        RETURN url;
    END IF;
END

CREATE FUNCTION `get_site`(site_id INTEGER) RETURNS varchar(50) CHARSET utf8mb4
    DETERMINISTIC
BEGIN
    IF site_id = 1 THEN
        RETURN '玩偶';
    ELSEIF site_id = 2 THEN
        RETURN '至臻视觉';
    ELSEIF site_id = 3 THEN
        RETURN '多多';
    ELSEIF site_id = 4 THEN
        RETURN '闪电';
    ELSEIF site_id = 5 THEN
        RETURN '木偶';
    ELSEIF site_id = 6 THEN
        RETURN '观影';
    ELSEIF site_id = 7 THEN
        RETURN '二小';   
    ELSEIF site_id = 8 THEN
        RETURN '蜡笔';
    ELSEIF site_id = 9 THEN
        RETURN '特下饭';
    ELSEIF site_id = 10 THEN
        RETURN '清影';
    ELSEIF site_id = 11 THEN
        RETURN '表哥';
    ELSEIF site_id = 12 THEN
        RETURN '百家';
    ELSEIF site_id = 13 THEN
        RETURN '欧歌';
    ELSEIF site_id = 14 THEN
        RETURN '大玩';
    ELSEIF site_id = 15 THEN
        RETURN '虎斑';
    ELSE
        RETURN '未知';
    END IF;
END
CREATE FUNCTION `get_type`(type_id INTEGER) RETURNS varchar(50) CHARSET utf8mb4
    DETERMINISTIC
BEGIN
    IF type_id = 1 THEN
        RETURN '电影';
    ELSEIF type_id = 2 THEN
        RETURN '电视剧';
    ELSEIF type_id = 3 THEN
        RETURN '动漫';
    ELSEIF type_id = 4 THEN
        RETURN '综艺';
    ELSEIF type_id = 5 THEN
        RETURN '纪录片';
    ELSEIF type_id = 6 THEN
        RETURN '音乐';
    ELSEIF type_id = 7 THEN
        RETURN '短剧';
    ELSEIF type_id = 8 THEN
        RETURN '臻彩';
    ELSEIF type_id = 9 THEN
        RETURN '网盘专区';
    ELSEIF type_id = 10 THEN
        RETURN '教程';
    ELSEIF type_id = 11 THEN
        RETURN '合集';
    ELSEIF type_id = 12 THEN
        RETURN '有声读物';
    ELSE
        RETURN cast(type_id as char);
    END IF;
END
CREATE FUNCTION `get_cloud_name`(cloud_id INTEGER) RETURNS varchar(50) CHARSET utf8mb4
    DETERMINISTIC
BEGIN
    IF cloud_id = 1 THEN
        RETURN '阿里云盘';
    ELSEIF cloud_id = 2 THEN
        RETURN '夸克网盘';
    ELSEIF cloud_id = 3 THEN
        RETURN 'UC网盘';
    ELSEIF cloud_id = 4 THEN
        RETURN '百度网盘';
    ELSEIF cloud_id = 5 THEN
        RETURN '迅雷网盘';
    ELSEIF cloud_id = 6 THEN
        RETURN '123网盘';
    ELSEIF cloud_id = 7 THEN
        RETURN '115网盘';
    ELSEIF cloud_id = 8 THEN
        RETURN '天翼云盘';
    ELSEIF cloud_id = 9 THEN
        RETURN '移动网盘';
    ELSE
        RETURN cast(cloud_id as char);
    END IF;
END