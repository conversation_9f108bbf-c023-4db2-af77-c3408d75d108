{"version": "5", "dialect": "mysql", "id": "36ee9354-dfe5-4d54-8d0e-c47907659793", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"cloud_files": {"name": "cloud_files", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "cloud_id": {"name": "cloud_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "vod_url_id": {"name": "vod_url_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "fid": {"name": "fid", "type": "char(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "pdir_fid": {"name": "pdir_fid", "type": "char(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "size": {"name": "size", "type": "bigint", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_type": {"name": "file_type", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "format_type": {"name": "format_type", "type": "char(32)", "primaryKey": false, "notNull": false, "autoincrement": false}, "duration": {"name": "duration", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "fps": {"name": "fps", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "video_height": {"name": "video_height", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "video_width": {"name": "video_width", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "video_max_resolution": {"name": "video_max_resolution", "type": "char(10)", "primaryKey": false, "notNull": false, "autoincrement": false}, "l_created_at": {"name": "l_created_at", "type": "bigint", "primaryKey": false, "notNull": false, "autoincrement": false}, "l_updated_at": {"name": "l_updated_at", "type": "bigint", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"cloud_files_id": {"name": "cloud_files_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "vod_down_list": {"name": "vod_down_list", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "vod_info_id": {"name": "vod_info_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "site_id": {"name": "site_id", "type": "tinyint", "primaryKey": false, "notNull": false, "autoincrement": false}, "site_vod_id": {"name": "site_vod_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "autoincrement": false}, "create_time": {"name": "create_time", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(40)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"ix_vod_down_list_url": {"name": "ix_vod_down_list_url", "columns": ["url"], "isUnique": false}, "vod_info_id": {"name": "vod_info_id", "columns": ["vod_info_id"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"vod_down_list_id": {"name": "vod_down_list_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "vod_infos": {"name": "vod_infos", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "director": {"name": "director", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false, "autoincrement": false}, "year": {"name": "year", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "pic": {"name": "pic", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false, "autoincrement": false}, "starring": {"name": "starring", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false, "autoincrement": false}, "type_id": {"name": "type_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "remark": {"name": "remark", "type": "<PERSON><PERSON><PERSON>(80)", "primaryKey": false, "notNull": false, "autoincrement": false}, "sub_title": {"name": "sub_title", "type": "<PERSON><PERSON><PERSON>(80)", "primaryKey": false, "notNull": false, "autoincrement": false}, "tags": {"name": "tags", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "tags2": {"name": "tags2", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false, "autoincrement": false}, "create_time": {"name": "create_time", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "update_time": {"name": "update_time", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "area": {"name": "area", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "bianju": {"name": "bianju", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "stime": {"name": "stime", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "times": {"name": "times", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "ename": {"name": "ename", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false, "autoincrement": false}, "douban_id": {"name": "douban_id", "type": "<PERSON><PERSON><PERSON>(8)", "primaryKey": false, "notNull": false, "autoincrement": false}, "imdb_id": {"name": "imdb_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "autoincrement": false}, "ro_id": {"name": "ro_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "tmdb_id": {"name": "tmdb_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "backdrop_path": {"name": "backdrop_path", "type": "char(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}}, "indexes": {"ix_title_year": {"name": "ix_title_year", "columns": ["title", "year"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"vod_infos_id": {"name": "vod_infos_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "vod_play_list": {"name": "vod_play_list", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "vod_info_id": {"name": "vod_info_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "site_id": {"name": "site_id", "type": "tinyint", "primaryKey": false, "notNull": false, "autoincrement": false}, "site_vod_id": {"name": "site_vod_id", "type": "char(4)", "primaryKey": false, "notNull": false, "autoincrement": false}, "line_id": {"name": "line_id", "type": "tinyint", "primaryKey": false, "notNull": false, "autoincrement": false}, "episode": {"name": "episode", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false, "autoincrement": false}, "create_time": {"name": "create_time", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"ix_site_vod_id": {"name": "ix_site_vod_id", "columns": ["site_vod_id", "line_id"], "isUnique": false}, "ix_vod_play_list_url": {"name": "ix_vod_play_list_url", "columns": ["url"], "isUnique": false}, "vod_info_id": {"name": "vod_info_id", "columns": ["vod_info_id"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"vod_play_list_id": {"name": "vod_play_list_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "vod_site_ids": {"name": "vod_site_ids", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "vod_info_id": {"name": "vod_info_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "site_id": {"name": "site_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "site_vod_id": {"name": "site_vod_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "type_id": {"name": "type_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "create_time": {"name": "create_time", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"ix_site_id": {"name": "ix_site_id", "columns": ["site_id", "site_vod_id"], "isUnique": false}, "ix_site_vod_id": {"name": "ix_site_vod_id", "columns": ["site_vod_id", "site_id"], "isUnique": false}, "vod_site_ids_FK_0_0": {"name": "vod_site_ids_FK_0_0", "columns": ["vod_info_id"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"vod_site_ids_id": {"name": "vod_site_ids_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "vod_urls": {"name": "vod_urls", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "cloudId": {"name": "cloudId", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "create_time": {"name": "create_time", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "vod_info_id": {"name": "vod_info_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "check_status": {"name": "check_status", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "is_invalid": {"name": "is_invalid", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "site_id": {"name": "site_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "pwd": {"name": "pwd", "type": "char(6)", "primaryKey": false, "notNull": false, "autoincrement": false}, "stoken": {"name": "stoken", "type": "char(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "invalid_reason": {"name": "invalid_reason", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"ix_url": {"name": "ix_url", "columns": ["url"], "isUnique": false}, "vod_urls_FK_0_0": {"name": "vod_urls_FK_0_0", "columns": ["vod_info_id"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"vod_urls_id": {"name": "vod_urls_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}