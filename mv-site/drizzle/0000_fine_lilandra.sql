CREATE TABLE `cloud_files` (
	`id` int AUTO_INCREMENT NOT NULL,
	`cloud_id` int,
	`vod_url_id` int,
	`fid` char(50),
	`pdir_fid` char(50),
	`file_name` varchar(255),
	`size` bigint,
	`file_type` int,
	`format_type` char(32),
	`duration` int,
	`fps` int,
	`video_height` int,
	`video_width` int,
	`video_max_resolution` char(10),
	`l_created_at` bigint,
	`l_updated_at` bigint,
	CONSTRAINT `cloud_files_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `vod_down_list` (
	`id` int AUTO_INCREMENT NOT NULL,
	`vod_info_id` int,
	`site_id` tinyint,
	`site_vod_id` varchar(10),
	`create_time` datetime,
	`url` varchar(40),
	CONSTRAINT `vod_down_list_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `vod_infos` (
	`id` int AUTO_INCREMENT NOT NULL,
	`title` varchar(50),
	`director` varchar(128),
	`year` int,
	`content` text,
	`pic` varchar(256),
	`starring` varchar(512),
	`type_id` int,
	`remark` varchar(80),
	`sub_title` varchar(80),
	`tags` varchar(50),
	`tags2` varchar(30),
	`create_time` datetime,
	`update_time` datetime,
	`area` varchar(50),
	`language` varchar(50),
	`bianju` varchar(100),
	`stime` varchar(100),
	`times` varchar(100),
	`ename` varchar(256),
	`douban_id` varchar(8),
	`imdb_id` varchar(10),
	`ro_id` varchar(100),
	`tmdb_id` int,
	`backdrop_path` char(50),
	`is_deleted` boolean DEFAULT false,
	CONSTRAINT `vod_infos_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `vod_play_list` (
	`id` int AUTO_INCREMENT NOT NULL,
	`vod_info_id` int,
	`site_id` tinyint,
	`site_vod_id` char(4),
	`line_id` tinyint,
	`episode` int,
	`url` varchar(256),
	`create_time` datetime,
	CONSTRAINT `vod_play_list_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `vod_site_ids` (
	`id` int AUTO_INCREMENT NOT NULL,
	`vod_info_id` int NOT NULL,
	`site_id` int NOT NULL,
	`site_vod_id` varchar(255),
	`type_id` int,
	`create_time` datetime,
	CONSTRAINT `vod_site_ids_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `vod_urls` (
	`id` int AUTO_INCREMENT NOT NULL,
	`cloudId` int,
	`url` varchar(100),
	`create_time` datetime,
	`vod_info_id` int,
	`check_status` int DEFAULT 0,
	`is_invalid` int DEFAULT 0,
	`site_id` int,
	`pwd` char(6),
	`stoken` char(50),
	`invalid_reason` varchar(100),
	CONSTRAINT `vod_urls_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE INDEX `ix_vod_down_list_url` ON `vod_down_list` (`url`);--> statement-breakpoint
CREATE INDEX `vod_info_id` ON `vod_down_list` (`vod_info_id`);--> statement-breakpoint
CREATE INDEX `ix_title_year` ON `vod_infos` (`title`,`year`);--> statement-breakpoint
CREATE INDEX `ix_site_vod_id` ON `vod_play_list` (`site_vod_id`,`line_id`);--> statement-breakpoint
CREATE INDEX `ix_vod_play_list_url` ON `vod_play_list` (`url`);--> statement-breakpoint
CREATE INDEX `vod_info_id` ON `vod_play_list` (`vod_info_id`);--> statement-breakpoint
CREATE INDEX `ix_site_id` ON `vod_site_ids` (`site_id`,`site_vod_id`);--> statement-breakpoint
CREATE INDEX `ix_site_vod_id` ON `vod_site_ids` (`site_vod_id`,`site_id`);--> statement-breakpoint
CREATE INDEX `vod_site_ids_FK_0_0` ON `vod_site_ids` (`vod_info_id`);--> statement-breakpoint
CREATE INDEX `ix_url` ON `vod_urls` (`url`);--> statement-breakpoint
CREATE INDEX `vod_urls_FK_0_0` ON `vod_urls` (`vod_info_id`);