import Link from "next/link";
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";
import { cn } from "@/lib/utils";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  basePath: string;
  searchParams?: Record<string, string>;
}

export function Pagination({ currentPage, totalPages, basePath, searchParams = {} }: PaginationProps) {
  // 如果总页数小于2，不显示分页
  if (totalPages < 2) return null;

  // 构建页面链接
  const createPageLink = (page: number) => {
    const params = new URLSearchParams();
    
    // 添加当前的搜索参数
    Object.entries(searchParams).forEach(([key, value]) => {
      if (key !== "page" && value) {
        params.append(key, value);
      }
    });
    
    // 添加页码参数（如果不是第一页）
    if (page > 1) {
      params.append("page", page.toString());
    }
    
    const queryString = params.toString();
    return `${basePath}${queryString ? `?${queryString}` : ""}`;
  };

  // 确定要显示哪些页码
  const pageNumbers: Array<number> = [];
  const maxPagesToShow = 5;
  
  if (totalPages <= maxPagesToShow) {
    // 如果总页数小于等于最大显示页数，显示所有页码
    for (let i = 1; i <= totalPages; i++) {
      pageNumbers.push(i);
    }
  } else {
    // 否则显示部分页码，并用省略号表示
    if (currentPage <= 3) {
      // 当前页接近开始
      for (let i = 1; i <= 4; i++) {
        pageNumbers.push(i);
      }
      pageNumbers.push(-1); // 表示省略号
      pageNumbers.push(totalPages);
    } else if (currentPage >= totalPages - 2) {
      // 当前页接近结束
      pageNumbers.push(1);
      pageNumbers.push(-1); // 表示省略号
      for (let i = totalPages - 3; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // 当前页在中间
      pageNumbers.push(1);
      pageNumbers.push(-1); // 表示省略号
      for (let i = currentPage - 1; i <= currentPage + 1; i++) {
        pageNumbers.push(i);
      }
      pageNumbers.push(-2); // 表示省略号
      pageNumbers.push(totalPages);
    }
  }

  return (
    <nav className="flex justify-center my-8">
      <ul className="flex items-center gap-1">
        {/* 上一页按钮 */}
        <li>
          <Link prefetch={false}
            href={currentPage > 1 ? createPageLink(currentPage - 1) : "#"}
            className={cn(
              "flex items-center justify-center w-10 h-10 rounded-md",
              currentPage > 1
                ? "hover:bg-accent"
                : "opacity-50 cursor-not-allowed"
            )}
            aria-disabled={currentPage <= 1}
            tabIndex={currentPage <= 1 ? -1 : undefined}
          >
            <ChevronLeft className="w-5 h-5" />
            <span className="sr-only">上一页</span>
          </Link>
        </li>

        {/* 页码按钮 */}
        {pageNumbers.map((page, index) => {
          if (page === -1 || page === -2) {
            // 显示省略号
            return (
              <li key={`ellipsis-${index}`}>
                <span className="flex items-center justify-center w-10 h-10">
                  <MoreHorizontal className="w-5 h-5" />
                </span>
              </li>
            );
          }

          return (
            <li key={page}>
              <Link prefetch={false}
                href={createPageLink(page)}
                className={cn(
                  "flex items-center justify-center w-10 h-10 rounded-md",
                  currentPage === page
                    ? "bg-primary text-primary-foreground font-medium"
                    : "hover:bg-accent"
                )}
                aria-current={currentPage === page ? "page" : undefined}
              >
                {page}
              </Link>
            </li>
          );
        })}

        {/* 下一页按钮 */}
        <li>
          <Link prefetch={false}
            href={currentPage < totalPages ? createPageLink(currentPage + 1) : "#"}
            className={cn(
              "flex items-center justify-center w-10 h-10 rounded-md",
              currentPage < totalPages
                ? "hover:bg-accent"
                : "opacity-50 cursor-not-allowed"
            )}
            aria-disabled={currentPage >= totalPages}
            tabIndex={currentPage >= totalPages ? -1 : undefined}
          >
            <ChevronRight className="w-5 h-5" />
            <span className="sr-only">下一页</span>
          </Link>
        </li>
      </ul>
    </nav>
  );
} 