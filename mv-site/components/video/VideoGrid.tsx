"use client";

import VideoCard from "./VideoCard";

interface Video {
  id: number;
  title: string | null;
  pic: string | null;
  year?: number | null;
  remark?: string | null;
}

interface VideoGridProps {
  videos: Video[];
  title?: string;
}

export default function VideoGrid({ videos, title }: VideoGridProps) {
  return (
    <section className="my-8">
      {title && <h2 className="text-2xl font-bold mb-4">{title}</h2>}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {videos.map((video) => (
          <VideoCard
            key={video.id}
            id={video.id}
            title={video.title}
            pic={video.pic}
            year={video.year}
            remark={video.remark}
          />
        ))}
      </div>
    </section>
  );
} 