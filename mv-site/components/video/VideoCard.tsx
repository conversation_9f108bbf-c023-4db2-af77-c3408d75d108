"use client";

import Link from "next/link";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";

interface VideoCardProps {
  id: number;
  title: string | null;
  pic: string | null;
  year?: number | null;
  remark?: string | null;
}

export default function VideoCard({ id, title, pic, year, remark }: VideoCardProps) {
  const displayTitle = title || "未知标题";
  let displayPic = pic;
  if (pic==null || pic==undefined || pic=="") {
    displayPic = "https://img.jukuku.top/default.jpg";
  } else {
    if (pic.startsWith('http://') || pic.startsWith('https://')) {
      displayPic = pic;
    } else {
      displayPic = 'https://img.jukuku.top/'+pic;
    }
  }
  
  // 判断是否为完整URL
  let isExternalImage = pic && (pic.startsWith('http://') || pic.startsWith('https://'));
  isExternalImage = true;
  return (
    <Link href={`/detail/${id}`} prefetch={false}>
      <Card className="overflow-hidden h-full transition-all hover:scale-105 hover:shadow-lg">
        <div className="aspect-[2/3] relative">
          {isExternalImage ? (
            // 对于外部图片链接，使用传统的img标签
            <img
              src={displayPic}
              alt={displayTitle}
              className="absolute inset-0 w-full h-full object-cover"
            />
          ) : (
            // 对于内部图片链接，继续使用Next.js的Image组件
            <Image
              src={displayPic}
              alt={displayTitle}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          )}
          {remark && (
            <div className="absolute top-2 right-2 bg-primary/80 text-primary-foreground text-xs px-2 py-1 rounded">
              {remark}
            </div>
          )}
        </div>
        <CardContent className="p-3">
          <h3 className="font-medium line-clamp-1">{displayTitle}</h3>
          {year && <p className="text-sm text-muted-foreground">{year}</p>}
        </CardContent>
      </Card>
    </Link>
  );
} 