"use client"

import { useEffect, useRef, useState } from "react"
import Artplayer from "artplayer"
import type { Option } from "artplayer/types/option"

interface ArtPlayerProps {
  option: Option
  getInstance?: (art: Artplayer) => void
  className?: string
}

export default function ArtPlayer({
  option,
  getInstance,
  className = "",
}: ArtPlayerProps) {
  const artRef = useRef<HTMLDivElement>(null)
  const [art, setArt] = useState<Artplayer | null>(null)

  useEffect(() => {
    if (!artRef.current) return

    const instance = new Artplayer({
      ...option,
      container: artRef.current,
    })

    setArt(instance)
    
    if (getInstance) {
      getInstance(instance)
    }

    return () => {
      if (instance && instance.destroy) {
        instance.destroy(true)
      }
    }
  }, [option, getInstance])

  return <div ref={artRef} className={`aspect-video ${className}`}></div>
} 