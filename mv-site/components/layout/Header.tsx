"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

// 视频类型定义
const VIDEO_TYPES = [
  { id: 1, name: "电影", path: "/category/movie" },
  { id: 2, name: "剧集", path: "/category/tv" },
  { id: 3, name: "动漫", path: "/category/anime" },
  { id: 4, name: "综艺", path: "/category/variety" },
  { id: 5, name: "纪录片", path: "/category/documentary" },
  { id: 7, name: "短剧", path: "/category/short" },
];

export default function Header() {
  const [searchKeyword, setSearchKeyword] = useState("");
  const router = useRouter();

  // 处理搜索表单提交
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchKeyword.trim()) {
      router.push(`/search?keyword=${encodeURIComponent(searchKeyword.trim())}`);
    }
  };

  return (
    <header className="bg-background border-b">
      <div className="container mx-auto py-4">
        <div className="flex flex-col md:flex-row items-center justify-between gap-4">
          {/* Logo */}
          <Link prefetch={false} href="/" className="text-2xl font-bold">
            影视网站
          </Link>

          {/* 搜索框 */}
          <form onSubmit={handleSearch} className="w-full md:w-1/3 flex">
            <Input
              type="text"
              placeholder="搜索影视剧..."
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              className="rounded-r-none"
            />
            <Button type="submit" className="rounded-l-none">
              搜索
            </Button>
          </form>
        </div>

        {/* 分类导航 */}
        <nav className="mt-4">
          <ul className="flex flex-wrap items-center gap-4 md:gap-8">
            {VIDEO_TYPES.map((type) => (
              <li key={type.id}>
                <Link prefetch={false}
                  href={type.path}
                  className="hover:text-primary transition-colors"
                >
                  {type.name}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </header>
  );
} 