"use client"

import { useRouter, usePathname, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface FilterBarProps {
  currentYear?: string
  currentArea?: string
}

// 定义年份和地区选项
const YEARS = [
  { value: "", label: "全部" },
  ...Array.from({ length: 20 }, (_, i) => {
    const year = new Date().getFullYear() - i
    return { value: year.toString(), label: year.toString() }
  }),
  { value: "earlier", label: "更早" }
]

const AREAS = [
  { value: "", label: "全部" },
  { value: "大陆", label: "大陆" },
  { value: "香港", label: "香港" },
  { value: "台湾", label: "台湾" },
  { value: "美国", label: "美国" },
  { value: "西班牙", label: "西班牙" },
  { value: "法国", label: "法国" },
  { value: "英国", label: "英国" },
  { value: "日本", label: "日本" },
  { value: "韩国", label: "韩国" },
  { value: "泰国", label: "泰国" },
  { value: "德国", label: "德国" },
  { value: "印度", label: "印度" },
  { value: "意大利", label: "意大利" },
  { value: "加拿大", label: "加拿大" },
  { value: "其它", label: "其它" }
]

export function FilterBar({ currentYear = "", currentArea = "" }: FilterBarProps) {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // 处理筛选变更
  const handleFilterChange = (type: "year" | "area", value: string) => {
    const params = new URLSearchParams(searchParams.toString())
    
    // 重置页码
    params.delete("page")
    
    // 设置或清除筛选参数
    if (value) {
      params.set(type, value)
    } else {
      params.delete(type)
    }
    
    // 导航到新URL
    router.push(`${pathname}?${params.toString()}`)
  }

  return (
    <div className="mb-6 space-y-4">
      {/* 年份筛选 */}
      <div className="flex flex-wrap items-center gap-2">
        <span className="font-medium min-w-16">年份：</span>
        <div className="flex flex-wrap gap-2">
          {YEARS.map((year) => (
            <Button
              key={year.value}
              variant="outline"
              size="sm"
              className={cn(
                "h-8",
                year.value === currentYear && "bg-primary text-primary-foreground"
              )}
              onClick={() => handleFilterChange("year", year.value)}
            >
              {year.label}
            </Button>
          ))}
        </div>
      </div>
      
      {/* 地区筛选 */}
      <div className="flex flex-wrap items-center gap-2">
        <span className="font-medium min-w-16">地区：</span>
        <div className="flex flex-wrap gap-2">
          {AREAS.map((area) => (
            <Button
              key={area.value}
              variant="outline"
              size="sm"
              className={cn(
                "h-8",
                area.value === currentArea && "bg-primary text-primary-foreground"
              )}
              onClick={() => handleFilterChange("area", area.value)}
            >
              {area.label}
            </Button>
          ))}
        </div>
      </div>
    </div>
  )
} 