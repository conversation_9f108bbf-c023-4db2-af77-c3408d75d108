"use client";

import Link from "next/link";
import Image from "next/image";
import React, { useEffect, useCallback } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";

interface BannerItem {
  id: number;
  title: string;
  pic: string;
  backdropPath?: string;
}

interface HeroBannerProps {
  items: BannerItem[];
}

export default function HeroBanner({ items }: HeroBannerProps) {
  const [api, setApi] = React.useState<CarouselApi>();
  const [current, setCurrent] = React.useState(0);

  // 自动切换轮播图
  const autoplay = useCallback(() => {
    if (!api) return;
    
    // 如果当前是最后一张，则回到第一张
    if (current === items.length - 1) {
      api.scrollTo(0);
    } else {
      api.scrollNext();
    }
  }, [api, current, items.length]);

  // 监听轮播图切换事件
  useEffect(() => {
    if (!api) return;

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap());
    });

    // 设置自动切换定时器
    const interval = setInterval(autoplay, 5000); // 每5秒切换一次

    return () => {
      clearInterval(interval);
    };
  }, [api, autoplay]);

  if (!items || items.length === 0) return null;

  return (
    <section className="w-full my-6">
      <Carousel className="w-full" setApi={setApi}>
        <CarouselContent>
          {items.map((item) => (
            <CarouselItem key={item.id}>
              <Link prefetch={false} href={`/detail/${item.id}`}>
                <div className="relative aspect-[21/9] w-full overflow-hidden rounded-lg">
                  <img
                    src={item.backdropPath || item.pic || "/placeholder-banner.jpg"}
                    alt={item.title}
                    className="object-cover"
                    sizes="100vw"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                  <div className="absolute bottom-0 left-0 p-4 md:p-6">
                    <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-white">
                      {item.title}
                    </h2>
                  </div>
                </div>
              </Link>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="hidden md:flex" />
        <CarouselNext className="hidden md:flex" />
      </Carousel>
    </section>
  );
} 