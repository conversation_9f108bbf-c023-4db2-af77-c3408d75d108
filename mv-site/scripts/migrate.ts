import 'dotenv/config';
import { createConnection } from 'mysql2';
import { drizzle } from 'drizzle-orm/mysql2';
import { migrate } from 'drizzle-orm/mysql2/migrator';

// 数据库连接配置
const connection = createConnection({
  host: process.env.DB_HOST || '*************',
  user: process.env.DB_USER || 'vod_db',
  password: process.env.DB_PASSWORD || '8XnJfRbbSF4Xaah5',
  database: process.env.DB_NAME || 'vod_db',
  port: Number(process.env.DB_PORT || 3306),
  multipleStatements: true,
});

// 创建Drizzle实例
const db = drizzle(connection);

// 运行迁移
async function main() {
  try {
    console.log('开始数据库迁移...');
    
    await migrate(db, { migrationsFolder: 'drizzle' });
    
    console.log('数据库迁移完成！');
  } catch (error) {
    console.error('数据库迁移失败:', error);
    process.exit(1);
  } finally {
    connection.end();
  }
}

main(); 