import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: '**',
      }
    ],
    // 保留特定域名配置，以防需要特殊处理
    domains: [
      "img.bfzypic.com",  // 允许的图片域名
      "pic.imgdb.cn",     // 可能的其他图片域名
      "img9.doubanio.com",
      "img1.doubanio.com",
      "img2.doubanio.com",
      "img3.doubanio.com"
    ],
  },
};

export default nextConfig;

// added by create cloudflare to enable calling `getCloudflareContext()` in `next dev`
import { initOpenNextCloudflareForDev } from '@opennextjs/cloudflare';
initOpenNextCloudflareForDev();
