import { notFound } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { getDbAsync } from "@/lib/db";
import { vodInfos, vodPlayList, vodUrls, vodDownList, vodPicInfo } from "@/db/schema";
import { eq, and, not, asc } from "drizzle-orm";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CopyButton } from "@/components/ui/copy-button";
import { ExternalLink } from "lucide-react";
import { Metadata } from "next";

// 定义接口
interface PlayListItem {
  id: number;
  episode?: number | null;
  lineId?: number | null;
  url?: string | null;
}

interface VodUrl {
  id: number;
  cloudId?: number | null;
  url?: string | null;
  pwd?: string | null;
}

interface VodDownItem {
  id: number;
  url?: string | null;
}

// 获取视频详情
async function getVideoDetail(id: number) {
  try {
    // 获取数据库连接
    const db = await getDbAsync();
    
    // 获取基本信息
    const videoResult = await db.select({
      id: vodInfos.id,
      title: vodInfos.title,
      pic: vodPicInfo.savePath || vodInfos.pic,
      year: vodInfos.year,
      content: vodInfos.content,
      subTitle: vodInfos.subTitle,
      typeId: vodInfos.typeId,
      director: vodInfos.director,
      starring: vodInfos.starring,
      area: vodInfos.area,
      language: vodInfos.language,
      stime: vodInfos.stime,
      remark: vodInfos.remark,
    })
      .from(vodInfos)
      .leftJoin(vodPicInfo, eq(vodInfos.id, vodPicInfo.vodInfoId))
      .where(
        and(
          eq(vodInfos.id, id),
          not(eq(vodInfos.isDeleted, true))
        )
      )
      .limit(1);
    
    if (!videoResult || videoResult.length === 0) {
      return null;
    }
    
    const video = videoResult[0];
    
    // 获取播放列表
    const playListResult = await db.select()
      .from(vodPlayList)
      .where(eq(vodPlayList.vodInfoId, id))
      .orderBy(asc(vodPlayList.episode));
    
    // 获取网盘链接
    const urlsResult = await db.select()
      .from(vodUrls)
      .where(
        and(
          eq(vodUrls.vodInfoId, id),
          eq(vodUrls.isInvalid, 0)
        )
      )
      .orderBy(asc(vodUrls.cloudId));
    
    // 获取下载链接
    const downListResult = await db.select()
      .from(vodDownList)
      .where(eq(vodDownList.vodInfoId, id));
    
    // 组合结果
    return {
      ...video,
      vodPlayList: playListResult,
      vodUrls: urlsResult,
      vodDownList: downListResult
    };
  } catch (error) {
    console.error(`获取视频详情失败 ID: ${id}:`, error);
    return null;
  }
}

// 生成元数据
export async function generateMetadata({ 
  params 
}: { 
  params: Promise<{ id: string }> 
}): Promise<Metadata> {
  const { id: idStr } = await params;
  const id = parseInt(idStr);
  if (isNaN(id)) return {};

  const video = await getVideoDetail(id);
  if (!video) return {};

  return {
    title: `${video.title}${video.year ? ` (${video.year})` : ""} - 影视网站`,
    description: video.content?.substring(0, 150) || `观看 ${video.title} 高清在线资源`,
  };
}

// 获取云存储名称
function getCloudName(cloudId: number | null | undefined): string {
  const cloudMap: Record<number, string> = {
    1: "阿里云盘",
    2: "夸克网盘",
    3: "UC网盘",
    4: "百度网盘",
    5: "迅雷网盘",
    6: "123云盘",
    7: "115网盘",
    8: "移动云盘",
    9: "天翼云盘",
    10: "磁力链接",
  };
  
  return cloudId ? cloudMap[cloudId] || `未知(${cloudId})` : "未知";
}

// 生成完整URL
function getFullUrl(cloudId: number | null | undefined, url: string | null | undefined): string {
  if (!url) return "";
  
  const cloudMap: Record<number, string> = {
    1: "https://www.alipan.com/s/",
    2: "https://pan.quark.cn/s/",
    3: "https://drive.uc.cn/s/",
    4: "https://pan.baidu.com/s/",
    5: "https://pan.xunlei.com/s/",
    6: "https://www.123pan.com/s/",
    7: "https://115cdn.com/s/",
    8: "https://caiyun.139.com/m/i?",
    9: "https://cloud.189.cn/t/",
    10: "magnet:?xt=urn:btih:",
  };
  
  if (cloudId && cloudMap[cloudId]) {
    return `${cloudMap[cloudId]}${url}`;
  }
  
  return url;
}

// 页面组件
export default async function DetailPage({
  params
}: {
  params: Promise<{ id: string }>;
}) {
  const { id: idStr } = await params;
  const id = parseInt(idStr);
  
  // 检查ID是否有效
  if (isNaN(id)) {
    notFound();
  }

  // 获取视频详情
  const video = await getVideoDetail(id);
  if (!video) {
    notFound();
  }

  // 视频类型映射
  const typeMap: Record<number, string> = {
    1: "电影",
    2: "剧集",
    3: "动漫",
    4: "综艺",
    5: "纪录片",
    7: "短剧",
  };

  // 按lineId分组的播放列表
  const playListByLine: Record<number, PlayListItem[]> = {};
  video.vodPlayList.forEach((item: PlayListItem) => {
    const lineId = item.lineId || 0;
    if (!playListByLine[lineId]) {
      playListByLine[lineId] = [];
    }
    playListByLine[lineId].push(item);
  });

  // 按cloudId分组的网盘资源
  const cloudUrlsByType: Record<number, VodUrl[]> = {};
  video.vodUrls.forEach((item: VodUrl) => {
    const cloudId = item.cloudId || 0;
    if (!cloudUrlsByType[cloudId]) {
      cloudUrlsByType[cloudId] = [];
    }
    cloudUrlsByType[cloudId].push(item);
  });

  // 磁力链接（cloudId=10）
  const magnetLinks = video.vodDownList.filter((item: { id: number; url?: string | null }) => item.url);

  video.pic = 'https://img.jukuku.top/'+video.pic;
  // 判断是否为完整URL
  let isExternalImage = video.pic && (video.pic.startsWith('http://') || video.pic.startsWith('https://'));
  return (
    <div className="max-w-6xl mx-auto">
      {/* 基本信息区域 */}
      <div className="flex flex-col md:flex-row gap-8 mb-10">
        {/* 封面图 */}
        <div className="w-full md:w-1/3 lg:w-1/4">
          <div className="aspect-[2/3] relative rounded-lg overflow-hidden">
            {isExternalImage ? (
              // 对于外部图片链接，使用传统的img标签
              <img
                src={video.pic || "/placeholder-image.jpg"}
                alt={video.title || ""}
                className="w-full h-full object-cover"
              />
            ) : (
              // 对于内部图片链接，继续使用Next.js的Image组件
              <Image
                src={video.pic || "/placeholder-image.jpg"}
                alt={video.title || ""}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 300px"
              />
            )}
          </div>
        </div>

        {/* 详细信息 */}
        <div className="flex-1">
          <h1 className="text-2xl md:text-3xl font-bold mb-2">
            {video.title}
            {video.year && <span className="ml-2">({video.year})</span>}
          </h1>
          
          {video.subTitle && (
            <p className="text-lg text-muted-foreground mb-4">{video.subTitle}</p>
          )}
          
          <div className="space-y-2 mb-6">
            {video.typeId && (
              <p>
                <span className="font-medium">类型：</span>
                {typeMap[video.typeId] || "未知"}
              </p>
            )}
            {video.director && (
              <p>
                <span className="font-medium">导演：</span>
                {video.director}
              </p>
            )}
            {video.starring && (
              <p>
                <span className="font-medium">主演：</span>
                {video.starring}
              </p>
            )}
            {video.area && (
              <p>
                <span className="font-medium">地区：</span>
                {video.area}
              </p>
            )}
            {video.language && (
              <p>
                <span className="font-medium">语言：</span>
                {video.language}
              </p>
            )}
            {video.stime && (
              <p>
                <span className="font-medium">上映：</span>
                {video.stime}
              </p>
            )}
            {video.remark && (
              <p>
                <span className="font-medium">备注：</span>
                {video.remark}
              </p>
            )}
          </div>
          
          {/* 简介 */}
          {video.content && (
            <div>
              <h2 className="text-xl font-bold mb-2">剧情简介</h2>
              <p className="text-muted-foreground whitespace-pre-line">{video.content}</p>
            </div>
          )}
        </div>
      </div>

      {/* 资源标签页 */}
      <Tabs 
        defaultValue={
          Object.keys(playListByLine).length > 0 
            ? "online-play" 
            : Object.keys(cloudUrlsByType).length > 0 
              ? "pan-resources" 
              : magnetLinks.length > 0 
                ? "magnet-links" 
                : "online-play"
        } 
        className="mb-10"
      >
        <TabsList className="mb-4">
          <TabsTrigger value="online-play">在线播放</TabsTrigger>
          <TabsTrigger value="pan-resources">网盘资源</TabsTrigger>
          <TabsTrigger value="magnet-links">磁力资源</TabsTrigger>
        </TabsList>
        
        {/* 在线播放 */}
        <TabsContent value="online-play">
          {Object.keys(playListByLine).length > 0 ? (
            <div>
              <Tabs defaultValue={Object.keys(playListByLine)[0]} className="w-full">
                <TabsList className="mb-4">
                  {Object.keys(playListByLine).map((lineId) => (
                    <TabsTrigger key={lineId} value={lineId}>
                      线路 {parseInt(lineId) + 1}
                    </TabsTrigger>
                  ))}
                </TabsList>
                
                {Object.entries(playListByLine).map(([lineId, episodes]) => (
                  <TabsContent key={lineId} value={lineId}>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                      {episodes.map((item) => {
                        return (
                          <Link prefetch={false}
                            key={item.id}
                            href={`/player/${item.id}`}
                            className="p-3 border rounded-md text-center hover:bg-accent cursor-pointer"
                          >
                            {video.typeId === 1 ? "播放" : `第 ${item.episode || '?'} 集`}
                          </Link>
                        );
                      })}
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </div>
          ) : (
            <p className="text-muted-foreground">暂无在线播放资源</p>
          )}
        </TabsContent>
        
        {/* 网盘资源 */}
        <TabsContent value="pan-resources">
          {Object.keys(cloudUrlsByType).length > 0 ? (
            <div>
              <Tabs defaultValue={Object.keys(cloudUrlsByType)[0]} className="w-full">
                <TabsList className="mb-4">
                  {Object.keys(cloudUrlsByType).map((cloudId) => (
                    <TabsTrigger key={cloudId} value={cloudId}>
                      {getCloudName(parseInt(cloudId))}
                    </TabsTrigger>
                  ))}
                </TabsList>
                
                {Object.entries(cloudUrlsByType).map(([cloudId, urls]) => (
                  <TabsContent key={cloudId} value={cloudId}>
                    <div className="space-y-3">
                      {urls.map((item) => {
                        const fullUrl = getFullUrl(parseInt(cloudId), item.url);
                        return (
                          <div key={item.id} className="flex items-center gap-4 p-3 border rounded-md">
                            <div className="flex-1 truncate">{fullUrl}</div>
                            {item.pwd && (
                              <div className="flex items-center gap-2">
                                <span className="text-sm">提取码: {item.pwd}</span>
                                <CopyButton value={item.pwd} label="复制码" />
                              </div>
                            )}
                            <div className="flex items-center gap-2">
                              <CopyButton value={fullUrl} />
                              <a 
                                href={fullUrl} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 w-9"
                              >
                                <ExternalLink size={16} />
                              </a>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </div>
          ) : (
            <p className="text-muted-foreground">暂无网盘资源</p>
          )}
        </TabsContent>
        
        {/* 磁力资源 */}
        <TabsContent value="magnet-links">
          {magnetLinks.length > 0 ? (
            <div className="space-y-3">
              {magnetLinks.map((item) => {
                const magnetUrl = getFullUrl(10, item.url);
                return (
                  <div key={item.id} className="flex items-center gap-4 p-3 border rounded-md">
                    <div className="flex-1 truncate">{magnetUrl}</div>
                    <div className="flex items-center gap-2">
                      <CopyButton value={magnetUrl} />
                      <a 
                        href={magnetUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 w-9"
                      >
                        <ExternalLink size={16} />
                      </a>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <p className="text-muted-foreground">暂无磁力资源</p>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
} 