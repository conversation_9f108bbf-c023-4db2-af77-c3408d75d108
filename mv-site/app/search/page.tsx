import { redirect } from "next/navigation";
import VideoGrid from "@/components/video/VideoGrid";
import { Pagination } from "@/components/ui/pagination";
import { getDbAsync } from "@/lib/db";
import { vodInfos, vodPicInfo } from "../../db/schema";
import { eq, not, like, desc, count, or, and } from "drizzle-orm";

// 每页显示的视频数量
const PAGE_SIZE = 24;

// 定义视频类型
interface Video {
  id: number;
  title: string | null;
  pic: string | null;
  year?: number | null;
  remark?: string | null;
}

// 定义搜索结果类型
interface SearchResult {
  videos: Video[];
  total: number;
  totalPages: number;
  currentPage: number;
  isLooseSearch?: boolean;
  error?: string;
}

// 获取搜索结果
async function getSearchResults(keyword: string, page: number = 1): Promise<SearchResult> {
  if (!keyword) return { videos: [], total: 0, totalPages: 0, currentPage: 1 };
  
  try {
    // 获取数据库连接
    const db = await getDbAsync();
    
    // 检查db对象是否正常
    if (!db || typeof db.select !== 'function') {
      console.error('数据库连接对象无效');
      return { videos: [], total: 0, totalPages: 0, currentPage: page, error: '数据库连接失败' };
    }
    
    // 计算要跳过的记录数
    const skip = (page - 1) * PAGE_SIZE;
    
    // 使用前缀匹配模式以便利用数据库索引
    // 使用 startsWith 代替 contains，相当于 SQL 中的 LIKE 'xxx%'
    const searchConditions = and(
      not(eq(vodInfos.isDeleted, true)),
      or(
        like(vodInfos.title, `${keyword}%`),
        like(vodInfos.director, `${keyword}%`),
        like(vodInfos.starring, `${keyword}%`)
      )
    );
    
    // 获取符合条件的总记录数
    const countQuery = db.select({ value: count() }).from(vodInfos);
    const totalResult = await countQuery.where(searchConditions);
    const total = totalResult[0]?.value || 0;
    
    // 计算总页数
    const totalPages = Math.ceil(total / PAGE_SIZE) || 1; // 确保至少有一页
    
    // 搜索视频 - 分步构建查询
    const selectQuery = db.select({
      id: vodInfos.id,
      title: vodInfos.title,
      pic: vodPicInfo.savePath || vodInfos.pic,
      year: vodInfos.year,
      remark: vodInfos.remark,
    }).from(vodInfos).leftJoin(vodPicInfo, eq(vodInfos.id, vodPicInfo.vodInfoId));
    
    const whereQuery = selectQuery.where(searchConditions);
    const orderQuery = whereQuery.orderBy(desc(vodInfos.updateTime));
    const offsetQuery = orderQuery.offset(skip);
    const videos = await offsetQuery.limit(PAGE_SIZE);
    
    // 如果前缀匹配没有结果，尝试使用包含匹配
    if (videos.length === 0 && page === 1) {
      // 包含匹配条件
      const containsConditions = and(
        not(eq(vodInfos.isDeleted, true)),
        or(
          like(vodInfos.title, `%${keyword}%`),
          like(vodInfos.director, `%${keyword}%`),
          like(vodInfos.starring, `%${keyword}%`)
        )
      );
      
      // 获取包含匹配的总记录数
      const containsCountQuery = db.select({ value: count() }).from(vodInfos);
      const containsTotalResult = await containsCountQuery.where(containsConditions);
      const containsTotal = containsTotalResult[0]?.value || 0;
      
      // 如果有包含匹配的结果
      if (containsTotal > 0) {
        // 计算总页数
        const containsTotalPages = Math.ceil(containsTotal / PAGE_SIZE) || 1;
        
        // 获取包含匹配的视频 - 分步构建查询
        const containsSelectQuery = db.select({
          id: vodInfos.id,
          title: vodInfos.title,
          pic: vodPicInfo.savePath || vodInfos.pic,
          year: vodInfos.year,
          remark: vodInfos.remark,
        }).from(vodInfos).leftJoin(vodPicInfo, eq(vodInfos.id, vodPicInfo.vodInfoId));
        
        const containsWhereQuery = containsSelectQuery.where(containsConditions);
        const containsOrderQuery = containsWhereQuery.orderBy(desc(vodInfos.updateTime));
        const containsOffsetQuery = containsOrderQuery.offset(0);
        const containsVideos = await containsOffsetQuery.limit(PAGE_SIZE);
        
        return {
          videos: containsVideos,
          total: containsTotal,
          totalPages: containsTotalPages,
          currentPage: 1,
          isLooseSearch: true
        };
      }
      
      // 如果包含匹配也没有结果，尝试更宽松的搜索条件（部分关键字）
      if (keyword.length > 1) {
        // 使用部分关键字进行前缀匹配
        const partialKeyword = keyword.substring(0, Math.max(2, Math.floor(keyword.length / 2)));
        
        const looseConditions = and(
          not(eq(vodInfos.isDeleted, true)),
          like(vodInfos.title, `${partialKeyword}%`)
        );
        
        // 获取宽松条件的总记录数
        const looseCountQuery = db.select({ value: count() }).from(vodInfos);
        const looseTotalResult = await looseCountQuery.where(looseConditions);
        const looseTotal = looseTotalResult[0]?.value || 0;
        
        // 计算宽松条件的总页数
        const looseTotalPages = Math.ceil(looseTotal / PAGE_SIZE) || 1;
        
        // 宽松搜索 - 分步构建查询
        const looseSelectQuery = db.select({
          id: vodInfos.id,
          title: vodInfos.title,
          pic: vodPicInfo.savePath || vodInfos.pic,
          year: vodInfos.year,
          remark: vodInfos.remark,
        }).from(vodInfos).leftJoin(vodPicInfo, eq(vodInfos.id, vodPicInfo.vodInfoId));
        
        const looseWhereQuery = looseSelectQuery.where(looseConditions);
        const looseOrderQuery = looseWhereQuery.orderBy(desc(vodInfos.updateTime));
        const looseOffsetQuery = looseOrderQuery.offset(0);
        const looseVideos = await looseOffsetQuery.limit(PAGE_SIZE);
        
        if (looseVideos.length > 0) {
          return {
            videos: looseVideos,
            total: looseTotal,
            totalPages: looseTotalPages,
            currentPage: 1,
            isLooseSearch: true
          };
        }
      }
    }
    
    return {
      videos,
      total,
      totalPages,
      currentPage: page
    };
  } catch (error) {
    console.error("搜索视频失败:", error);
    return { videos: [], total: 0, totalPages: 0, currentPage: page, error: (error as Error).message };
  }
}

// 页面组件
export default async function SearchPage({
  searchParams,
}: {
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  try {
    // 获取关键字和页码
    const resolvedSearchParams = await searchParams;
    const keyword = typeof resolvedSearchParams.keyword === 'string' ? resolvedSearchParams.keyword.trim() : "";
    const page = typeof resolvedSearchParams.page === 'string' ? parseInt(resolvedSearchParams.page) : 1;
    
    // 如果没有关键字，重定向到首页
    if (!keyword) {
      redirect("/");
    }
    
    // 获取搜索结果
    const { videos, total, totalPages, currentPage, isLooseSearch, error } = await getSearchResults(keyword, page);
    
    // 如果有错误
    if (error) {
      return (
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold mb-4">搜索出错</h1>
          <p className="text-lg text-red-500">搜索失败: {error}</p>
        </div>
      );
    }

    return (
      <div>
        <h1 className="text-2xl font-bold mb-4">
          搜索 "{keyword}" 的结果
        </h1>
        
        <div className="mb-6 text-sm text-muted-foreground">
          共找到 {total} 条结果，第 {currentPage}/{totalPages} 页
          {isLooseSearch && (
            <span className="ml-2 text-yellow-500">
              (使用了模糊匹配)
            </span>
          )}
        </div>
        
        {videos.length > 0 ? (
          <>
            <VideoGrid videos={videos} />
            <Pagination 
              currentPage={currentPage} 
              totalPages={totalPages} 
              basePath="/search" 
              searchParams={{ keyword }}
            />
          </>
        ) : (
          <div className="text-center py-12">
            <p className="text-lg text-muted-foreground">
              没有找到与 "{keyword}" 相关的视频
            </p>
            <p className="mt-2 text-sm text-muted-foreground">
              建议尝试其他关键词或检查拼写
            </p>
          </div>
        )}
      </div>
    );
  } catch (error) {
    console.error("搜索页面渲染失败:", error);
    return (
      <div className="p-8 text-center">
        <h1 className="text-2xl font-bold mb-4">加载失败</h1>
        <p className="text-lg text-red-500">数据加载出错，请稍后再试</p>
      </div>
    );
  }
} 