import { notFound } from "next/navigation";
import VideoGrid from "@/components/video/VideoGrid";
import { Pagination } from "@/components/ui/pagination";
import { FilterBar } from "@/components/category/FilterBar";
import { getDbAsync } from "@/lib/db";
import { vodInfos, vodPicInfo } from "@/db/schema";
import { eq, not, lt, notInArray, desc, count, and, inArray } from "drizzle-orm";
import { Metadata } from "next";

// 每页显示的视频数量
const PAGE_SIZE = 24;

// 类型映射
const TYPE_MAP: Record<string, { id: number; title: string }> = {
  movie: { id: 1, title: "电影" },
  tv: { id: 2, title: "剧集" },
  anime: { id: 3, title: "动漫" },
  variety: { id: 4, title: "综艺" },
  documentary: { id: 5, title: "纪录片" },
  short: { id: 7, title: "短剧" },
};

// 生成静态参数
export function generateStaticParams() {
  return Object.keys(TYPE_MAP).map((type) => ({ type }));
}

// 构建筛选条件
function buildFilterConditions(typeId: number, year?: string, area?: string) {
  // 基础条件：未删除且类型匹配
  let conditions = and(
    not(eq(vodInfos.isDeleted, true)),
    eq(vodInfos.typeId, typeId)
  );

  // 年份筛选
  if (year) {
    if (year === "earlier") {
      // "更早"表示早于当前年份减去20年
      const earlierThan = new Date().getFullYear() - 20;
      conditions = and(conditions, lt(vodInfos.year, earlierThan));
    } else {
      conditions = and(conditions, eq(vodInfos.year, parseInt(year)));
    }
  }

  // 地区筛选
  if (area) {
    if (area === "其它") {
      // "其它"表示不在主要地区列表中的地区
      const mainAreas = [
        "大陆", "香港", "台湾", "美国", "西班牙", 
        "法国", "英国", "日本", "韩国", "泰国", 
        "德国", "印度", "意大利", "加拿大"
      ];
      conditions = and(conditions, notInArray(vodInfos.area, mainAreas));
    } else {
      if (area==='大陆'){
        conditions = and(conditions, inArray(vodInfos.area, ['大陆', '中国大陆','中国']));
      } else if (area==='香港'){
        conditions = and(conditions, inArray(vodInfos.area, ['香港', '中国香港']));
      } else if (area==='台湾'){
        conditions = and(conditions, inArray(vodInfos.area, ['台湾', '中国台湾']));
      } else {
        conditions = and(conditions, eq(vodInfos.area, area));
      }
    }
  }

  return conditions;
}

// 获取分类视频总数
async function getCategoryVideoCount(typeId: number, year?: string, area?: string) {
  try {
    // 获取数据库连接
    const db = await getDbAsync();
    
    const conditions = buildFilterConditions(typeId, year, area);
    const result = await db.select({ value: count() }).from(vodInfos).where(conditions);
    return result[0]?.value || 0;
  } catch (error) {
    console.error(`获取分类 ${typeId} 视频总数失败:`, error);
    return 0;
  }
}

// 获取分类视频数据（分页）
async function getCategoryVideos(
  typeId: number, 
  page: number = 1, 
  pageSize: number = PAGE_SIZE,
  year?: string,
  area?: string
) {
  const skip = (page - 1) * pageSize;
  
  try {
    // 获取数据库连接
    const db = await getDbAsync();
    
    const conditions = buildFilterConditions(typeId, year, area);
    const videos = await db.select({
      id: vodInfos.id,
      title: vodInfos.title,
      pic: vodPicInfo.savePath || vodInfos.pic,
      year: vodInfos.year,
      remark: vodInfos.remark,
      area: vodInfos.area,
    })
    .from(vodInfos)
    .leftJoin(vodPicInfo, eq(vodInfos.id, vodPicInfo.vodInfoId))
    .where(conditions)
    .orderBy(desc(vodInfos.updateTime))
    .offset(skip)
    .limit(pageSize);
    
    return videos;
  } catch (error) {
    console.error(`获取分类 ${typeId} 视频数据失败:`, error);
    return [];
  }
}

// 生成元数据
export async function generateMetadata({ 
  params 
}: { 
  params: Promise<{ type: string }> 
}): Promise<Metadata> {
  const { type: typeParam } = await params;
  const type = TYPE_MAP[typeParam];
  if (!type) return {};
  
  return {
    title: `${type.title} - 影视网站`,
    description: `最新热门${type.title}资源，高清在线观看`,
  };
}

// 页面组件
export default async function CategoryPage({
  params,
  searchParams,
}: {
  params: Promise<{ type: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  // 获取类型参数
  const { type: typeParam } = await params;
  
  // 检查类型是否存在
  const type = TYPE_MAP[typeParam];
  if (!type) {
    notFound();
  }

  // 获取当前页码和筛选条件
  const resolvedSearchParams = await searchParams;
  const page = resolvedSearchParams.page;
  const currentPage = typeof page === 'string' ? parseInt(page) : 1;
  const currentYear = typeof resolvedSearchParams.year === 'string' ? resolvedSearchParams.year : "";
  const currentArea = typeof resolvedSearchParams.area === 'string' ? resolvedSearchParams.area : "";
  
  // 获取分类视频总数
  const totalVideos = await getCategoryVideoCount(type.id, currentYear, currentArea);
  const totalPages = Math.ceil(totalVideos / PAGE_SIZE);
  
  // 获取分类视频
  const videos = await getCategoryVideos(type.id, currentPage, PAGE_SIZE, currentYear, currentArea);
  
  // 预先构建基础路径和查询参数
  const basePath = `/category/${typeParam}`;
  const searchParamsForPagination: Record<string, string> = {};
  if (currentYear) searchParamsForPagination.year = currentYear;
  if (currentArea) searchParamsForPagination.area = currentArea;

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">{type.title}</h1>
      
      {/* 筛选栏 */}
      <FilterBar currentYear={currentYear} currentArea={currentArea} />
      
      <div className="mb-4 text-sm text-muted-foreground">
        共找到 {totalVideos} 部{type.title}，共 {totalPages} 页
      </div>
      
      <VideoGrid videos={videos} />
      
      <Pagination 
        currentPage={currentPage} 
        totalPages={totalPages} 
        basePath={basePath} 
        searchParams={searchParamsForPagination}
      />
    </div>
  );
} 