import HeroBanner from "@/components/home/<USER>";
import VideoGrid from "@/components/video/VideoGrid";
import { getDbAsync } from "@/lib/db";
import { eq, isNull, not, desc, or, and, sql } from "drizzle-orm";
import { vodInfos, vodPicInfo, recentHot } from "../db/schema";

// 获取轮播图数据
async function getBannerVideos() {
  try {
    // 获取数据库连接
    const db = await getDbAsync();
    
    // 检查db对象是否正常
    if (!db || typeof db.select !== 'function') {
      console.error('数据库连接对象无效');
      return [];
    }
    
    // 从最近热门表中获取电影和电视剧各取id前8个
    const query1 = db.select({
      id: vodInfos.id,
      title: vodInfos.title,
      pic: vodPicInfo.savePath || vodInfos.pic,
      backdropPath: vodInfos.backdropPath,
      typeId: vodInfos.typeId,
      rating: recentHot.rating,
    })
    .from(recentHot)
    .innerJoin(vodInfos, eq(recentHot.vodInfoId, vodInfos.id))
    .leftJoin(vodPicInfo, eq(vodInfos.id, vodPicInfo.vodInfoId))
    .where(and(
      not(eq(vodInfos.isDeleted, true)),
      not(isNull(vodInfos.backdropPath)),
      or(
        eq(vodInfos.typeId, 1),  // 电影
      )
    ));
    
    // 添加排序和限制
    const videos = await query1
      .orderBy(recentHot.id)
      .limit(8);

    const query2 = db.select({
      id: vodInfos.id,
      title: vodInfos.title,
      pic: vodPicInfo.savePath || vodInfos.pic,
      backdropPath: vodInfos.backdropPath,
      typeId: vodInfos.typeId,
      rating: recentHot.rating,
    })
    .from(recentHot)
    .innerJoin(vodInfos, eq(recentHot.vodInfoId, vodInfos.id))
    .leftJoin(vodPicInfo, eq(vodInfos.id, vodPicInfo.vodInfoId))
    .where(and(
      not(eq(vodInfos.isDeleted, true)),
      not(isNull(vodInfos.backdropPath)),
      or(
        eq(vodInfos.typeId, 2)   // 电视剧
      )
    ));

    const videos2 = await query2
      .orderBy(recentHot.id)
      .limit(8);
    
    // 合并并确保返回的数据符合 BannerItem 类型
    return [...videos, ...videos2].map(video => ({
      id: video.id,
      title: video.title || "未知标题",
      pic: 'https://img.jukuku.top/'+video.pic || "/placeholder-image.jpg",
      backdropPath: 'https://image.tmdb.org/t/p/original'+video.backdropPath || undefined
    }));
  } catch (error) {
    console.error("获取轮播图数据失败:", error);
    return [];
  }
}

// 获取分类视频数据
async function getCategoryVideos(typeId: number, limit: number = 12) {
  try {
    // 获取数据库连接
    const db = await getDbAsync();
    
    // 检查db对象是否正常
    if (!db || typeof db.select !== 'function') {
      console.error('数据库连接对象无效');
      return [];
    }
    
    const query = db.select({
      id: vodInfos.id,
      title: vodInfos.title,
      pic: vodPicInfo.savePath || vodInfos.pic,
      year: vodInfos.year,
      remark: vodInfos.remark,
    })
    .from(vodInfos).leftJoin(vodPicInfo, eq(vodInfos.id, vodPicInfo.vodInfoId));
    
    // 添加where条件
    const whereQuery = query.where(
      not(eq(vodInfos.isDeleted, true)) && 
      eq(vodInfos.typeId, typeId)
    );
    
    // 添加排序和限制
    const videos = await whereQuery
      .orderBy(desc(vodInfos.updateTime))
      .limit(limit);
    
    return videos;
  } catch (error) {
    console.error(`获取分类 ${typeId} 视频数据失败:`, error);
    return [];
  }
}

export default async function Home() {
  try {
    // 获取轮播图数据
    const bannerVideos = await getBannerVideos();

    // 获取各分类最新视频
    const movieVideos = await getCategoryVideos(1); // 电影
    const tvVideos = await getCategoryVideos(2);    // 剧集
    const animeVideos = await getCategoryVideos(3); // 动漫
    const varietyVideos = await getCategoryVideos(4); // 综艺
    const documentaryVideos = await getCategoryVideos(5); // 纪录片
    const shortVideos = await getCategoryVideos(7); // 短剧

    return (
      <>
        {/* 轮播图 */}
        <HeroBanner items={bannerVideos} />

        {/* 各分类视频列表 */}
        <VideoGrid videos={movieVideos} title="最新电影" />
        <VideoGrid videos={tvVideos} title="最新剧集" />
        <VideoGrid videos={animeVideos} title="最新动漫" />
        <VideoGrid videos={varietyVideos} title="最新综艺" />
        <VideoGrid videos={documentaryVideos} title="最新纪录片" />
        <VideoGrid videos={shortVideos} title="最新短剧" />
      </>
    );
  } catch (error) {
    console.error("首页渲染失败:", error);
    return (
      <div className="p-8 text-center">
        <h1 className="text-2xl font-bold mb-4">加载失败</h1>
        <p className="text-lg text-red-500">数据加载出错，请稍后再试</p>
      </div>
    );
  }
}
