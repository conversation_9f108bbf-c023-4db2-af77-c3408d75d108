"use client"

import { useRouter } from "next/navigation"
import { useEffect, useRef, useState } from "react"
import Link from "next/link"
import ArtPlayer from "@/components/video/art-player"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Hls from "hls.js"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface PlayItem {
  id: number;
  lineId?: number | null;
  episode?: number | null;
  url?: string | null;
}

interface PlayerClientProps {
  url: string;
  title: string;
  currentId: number;
  currentLineId: number;
  currentEpisode: number;
  playlistByLine: Record<string, PlayItem[]>;
  videoType: number;
}

export default function PlayerClient({ 
  url, 
  title, 
  currentId,
  currentLineId,
  currentEpisode,
  playlistByLine,
  videoType
}: PlayerClientProps) {
  const router = useRouter()
  const playerRef = useRef<any>(null)
  const hlsInstancesRef = useRef<Hls[]>([])

  // 检查URL是否是m3u8格式
  const isHlsUrl = url.includes('.m3u8')

  // 获取播放器实例
  const getInstance = (art: any) => {
    playerRef.current = art
  }

  // 使用HLS.js加载m3u8
  useEffect(() => {
    let hls: Hls | null = null;
    
    if (isHlsUrl && playerRef.current && Hls.isSupported()) {
      hls = new Hls({
        // 添加额外配置以确保可以正确停止下载
        maxBufferLength: 30,
        maxMaxBufferLength: 60
      })
      hls.loadSource(url)
      hls.attachMedia(playerRef.current.video)
      
      // 存储hls实例以便后续清理
      if (playerRef.current) {
        playerRef.current.hls = hls;
        // 将hls实例添加到引用数组中
        hlsInstancesRef.current.push(hls);
      }
      
      // 可选：添加HLS事件监听
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS manifest parsed, ready to play')
        playerRef.current.play()
      })
    }
    
    // 清理函数
    return () => {
      if (hls) {
        // 停止所有加载和网络活动
        hls.stopLoad();
        hls.destroy()
        hls = null
        
        // 从引用数组中移除
        hlsInstancesRef.current = hlsInstancesRef.current.filter(h => h !== hls);
      }
      
      // 确保视频元素停止播放
      if (playerRef.current) {
        playerRef.current.pause()
        // 设置音量为0，避免可能的音频延迟
        playerRef.current.volume = 0
        // 尝试释放视频资源
        if (playerRef.current.video) {
          playerRef.current.video.src = ''
          playerRef.current.video.load()
        }
        // 清空HLS实例引用
        if (playerRef.current.hls) {
          playerRef.current.hls = null
        }
      }
    }
  }, [url, isHlsUrl])

  // 处理集数选择
  const handleEpisodeChange = (episodeId: string) => {
    router.push(`/player/${episodeId}`)
  }

  // 确保在组件卸载时正确销毁播放器
  useEffect(() => {
    return () => {
      // 清理所有HLS实例
      hlsInstancesRef.current.forEach(hls => {
        if (hls) {
          hls.stopLoad()
          hls.destroy()
        }
      });
      hlsInstancesRef.current = [];
      
      if (playerRef.current) {
        console.log('组件卸载，销毁播放器实例')
        // 停止播放
        playerRef.current.pause()
        // 设置音量为0
        playerRef.current.volume = 0
        // 销毁HLS实例如果存在
        if (playerRef.current.hls) {
          playerRef.current.hls.stopLoad()
          playerRef.current.hls.destroy()
          playerRef.current.hls = null
        }
        // 移除视频源
        if (playerRef.current.video) {
          playerRef.current.video.src = ''
          playerRef.current.video.load()
        }
        // 销毁播放器实例
        playerRef.current.destroy()
        playerRef.current = null
      }
    }
  }, [])

  // 处理返回按钮点击
  const handleBack = () => {
    // 先停止视频播放
    if (playerRef.current) {
      playerRef.current.pause()
      // 设置音量为0，避免可能的音频延迟
      playerRef.current.volume = 0
      // 销毁HLS实例如果存在
      if (playerRef.current.hls) {
        playerRef.current.hls.stopLoad()
        playerRef.current.hls.destroy()
        playerRef.current.hls = null
      }
      // 清除视频源
      if (playerRef.current.video) {
        playerRef.current.video.src = ''
        playerRef.current.video.load()
      }
    }
    
    // 清理所有HLS实例
    hlsInstancesRef.current.forEach(hls => {
      if (hls) {
        hls.stopLoad()
        hls.destroy()
      }
    });
    hlsInstancesRef.current = [];
    
    // 然后再返回
    router.back()
  }

  // 获取所有线路
  const lines = Object.keys(playlistByLine)

  return (
    <div className="container mx-auto py-4">
      <div className="mb-4">
        <Button variant="outline" size="sm" onClick={handleBack} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" /> 返回
        </Button>
        <h1 className="text-xl font-bold">{title}</h1>
      </div>
      
      <div className="rounded-lg overflow-hidden shadow-lg">
        <ArtPlayer
          option={{
            url,
            title,
            volume: 0.8,
            autoplay: false,
            pip: true,
            autoSize: true,
            autoMini: true,
            screenshot: true,
            setting: true,
            hotkey: true,
            playbackRate: true,
            aspectRatio: true,
            fullscreen: true,
            fullscreenWeb: true,
            subtitleOffset: true,
            miniProgressBar: true,
            mutex: true,
            backdrop: true,
            playsInline: true,
            autoPlayback: true,
            airplay: true,
            theme: '#23ade5',
            lang: 'zh-CN',
            moreVideoAttr: {
              crossOrigin: 'anonymous',
            },
            customType: {
              m3u8: function (video: HTMLVideoElement, url: string) {
                if (Hls.isSupported()) {
                  const hls = new Hls({
                    maxBufferLength: 30,
                    maxMaxBufferLength: 60
                  });
                  hls.loadSource(url);
                  hls.attachMedia(video);
                  
                  // 将hls实例保存到播放器引用中以便后续清理
                  if (playerRef.current && !playerRef.current.hls) {
                    playerRef.current.hls = hls;
                  }
                  
                  // 添加到HLS实例引用数组
                  hlsInstancesRef.current.push(hls);
                  
                  // 可选：添加错误处理
                  hls.on(Hls.Events.ERROR, function (event, data) {
                    console.log('HLS错误:', data);
                    if (data.fatal) {
                      switch(data.type) {
                        case Hls.ErrorTypes.NETWORK_ERROR:
                          console.log('网络错误，尝试恢复...');
                          hls.startLoad();
                          break;
                        case Hls.ErrorTypes.MEDIA_ERROR:
                          console.log('媒体错误，尝试恢复...');
                          hls.recoverMediaError();
                          break;
                        default:
                          console.log('无法恢复的错误');
                          hls.destroy();
                          // 从引用数组中移除
                          hlsInstancesRef.current = hlsInstancesRef.current.filter(h => h !== hls);
                          break;
                      }
                    }
                  });
                } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                  // 对于Safari浏览器，使用原生HLS支持
                  video.src = url;
                }
              }
            }
          } as any}
          getInstance={getInstance}
          className="w-full"
        />
      </div>

      {/* 线路和集数选择 */}
      <div className="mt-6">
        {lines.length > 1 && (
          <Tabs defaultValue={currentLineId.toString()} className="mb-6">
            <div className="flex items-center mb-2">
              <h3 className="text-lg font-medium mr-2">线路选择:</h3>
              <TabsList>
                {lines.map((lineId) => (
                  <TabsTrigger key={lineId} value={lineId}>
                    线路 {parseInt(lineId) + 1}
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>
            
            {lines.map((lineId) => (
              <TabsContent key={lineId} value={lineId}>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-8 gap-2">
                  {playlistByLine[lineId].map((item) => (
                    <Link prefetch={false}
                      key={item.id}
                      href={`/player/${item.id}`}
                      className={`p-2 border rounded text-center text-sm ${
                        item.id === currentId 
                          ? 'bg-primary text-primary-foreground' 
                          : 'hover:bg-accent'
                      }`}
                    >
                      {videoType === 1 ? "播放" : `第${item.episode || '?'}集`}
                    </Link>
                  ))}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        )}
        
        {lines.length === 1 && playlistByLine[lines[0]].length > 1 && (
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">选集:</h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-8 gap-2">
              {playlistByLine[lines[0]].map((item) => (
                <Link prefetch={false}
                  key={item.id}
                  href={`/player/${item.id}`}
                  className={`p-2 border rounded text-center text-sm ${
                    item.id === currentId 
                      ? 'bg-primary text-primary-foreground' 
                      : 'hover:bg-accent'
                  }`}
                >
                  {videoType === 1 ? "播放" : `第${item.episode || '?'}集`}
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 