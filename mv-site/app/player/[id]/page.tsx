import { notFound } from "next/navigation";
import { getDbAsync } from "@/lib/db";
import { vodPlayList, vodInfos } from "@/db/schema";
import { eq, asc } from "drizzle-orm";
import PlayerClient from "./player-client";
import { Metadata } from "next";

// 页面组件
export default async function PlayerPage({
  params
}: {
  params: Promise<{ id: string }>;
}) {
  const { id: idStr } = await params;
  const id = parseInt(idStr);
  
  if (isNaN(id)) {
    notFound();
  }

  try {
    // 获取数据库连接
    const db = await getDbAsync();
    
    // 根据ID获取播放项信息
    const playItemResults = await db.select({
      id: vodPlayList.id,
      url: vodPlayList.url,
      vodInfoId: vodPlayList.vodInfoId,
      lineId: vodPlayList.lineId,
      episode: vodPlayList.episode,
      videoTitle: vodInfos.title,
      videoTypeId: vodInfos.typeId
    })
    .from(vodPlayList)
    .leftJoin(vodInfos, eq(vodPlayList.vodInfoId, vodInfos.id))
    .where(eq(vodPlayList.id, id))
    .limit(1);
    
    const playItem = playItemResults[0];
    
    if (!playItem || !playItem.url || !playItem.vodInfoId) {
      notFound();
    }

    // 获取相同视频的所有播放列表项，按线路和集数分组
    const allPlayItems = await db.select()
      .from(vodPlayList)
      .where(eq(vodPlayList.vodInfoId, playItem.vodInfoId))
      .orderBy(asc(vodPlayList.lineId), asc(vodPlayList.episode));

    // 按线路分组
    const playlistByLine: Record<string, typeof allPlayItems> = {};
    allPlayItems.forEach(item => {
      const lineKey = `${item.lineId || 0}`;
      if (!playlistByLine[lineKey]) {
        playlistByLine[lineKey] = [];
      }
      playlistByLine[lineKey].push(item);
    });

    // 获取视频信息和剧集信息
    const videoTitle = playItem.videoTitle || "未知视频";
    const episodeText = playItem.videoTypeId !== 1 && playItem.episode 
      ? `第${playItem.episode}集` 
      : "";
    const title = `${videoTitle} ${episodeText}`.trim();

    return (
      <PlayerClient 
        url={playItem.url} 
        title={title}
        currentId={playItem.id}
        currentLineId={playItem.lineId || 0}
        currentEpisode={playItem.episode || 0}
        playlistByLine={playlistByLine}
        videoType={playItem.videoTypeId || 1}
      />
    );
  } catch (error) {
    console.error(`获取播放项失败 ID: ${id}:`, error);
    notFound();
  }
} 