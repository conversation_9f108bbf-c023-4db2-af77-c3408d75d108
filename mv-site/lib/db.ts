import { getCloudflareContext } from "@opennextjs/cloudflare";
import { drizzle } from "drizzle-orm/mysql2";
import { cache } from "react";
import { createConnection } from "mysql2";
import * as schema from "../db/schema";
 
export const getDb = cache(() => {
  const { env } = getCloudflareContext();
  const connection = createConnection({
    host: env.HYPERDRIVE.host,
    user: env.HYPERDRIVE.user,
    password: env.HYPERDRIVE.password,
    database: env.HYPERDRIVE.database,
    port: env.HYPERDRIVE.port,
  });
  return drizzle(connection);
});
 
// This is the one to use for static routes (i.e. ISR/SSG)
export const getDbAsync = cache(async () => {
  const { env } = await getCloudflareContext({ async: true });
  const connection = await createConnection({
    host: env.HYPERDRIVE.host,
    user: env.HYPERDRIVE.user,
    password: env.HYPERDRIVE.password,
    database: env.HYPERDRIVE.database,
    port: env.HYPERDRIVE.port,

    // Required to enable mysql2 compatibility for Workers
    disableEval: true,
  });
  return drizzle(connection);
});